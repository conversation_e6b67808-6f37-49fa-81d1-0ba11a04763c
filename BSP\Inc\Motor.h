#ifndef __Motor_h
#define __Motor_h

#include "SysConfig.h"

#define PWM_MAX 30

/**
 * @brief 电机编号
 *
 */
typedef enum
{
    MOTOR_FONT_LEFT = 0, //左前轮
    MOTOR_FONT_RIGHT, //右前轮
    MOTOR_BACK_LEFT, //左后轮
    MOTOR_BACK_RIGHT //右后轮
} MOTOR_Def_t;

/**
 * @brief 电机方向
 *
 */
typedef enum
{
    DIRC_NONE = 0,
    DIRC_FOWARD, //正转
    DIRC_BACKWARD //反转
} DIRC_Def_t;

void Motor_Start(void);
bool Motor_SetPWM(MOTOR_Def_t Motor_Num, uint8_t PWM_Duty);
bool Motor_SetDirc(MOTOR_Def_t Motor_Num, MOTOR_Def_t DIRC);
int16_t read_encoder(MOTOR_Def_t Motor_Num);
void Load_Motor_PWM(int MOTOR1_PWM,int MOTOR2_PWM);

#endif