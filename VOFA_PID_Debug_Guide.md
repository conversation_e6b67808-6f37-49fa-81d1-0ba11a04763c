# VOFA PID调试功能使用指南

## 功能概述
本系统已集成VOFA调试功能，支持通过串口实时调整PID参数，无需重新编译和下载程序。

## 支持的命令

### 位置环参数调整
- `LKP:数值` - 设置位置环Kp参数 (范围: 0-10)
- `LKI:数值` - 设置位置环Ki参数 (范围: 0-1)  
- `LKD:数值` - 设置位置环Kd参数 (范围: 0-1)

### 速度环参数调整
- `VKP:数值` - 设置速度环Kp参数 (范围: 0-10)
- `VKI:数值` - 设置速度环Ki参数 (范围: 0-1)
- `VKD:数值` - 设置速度环Kd参数 (范围: 0-1)

### 系统命令
- `GET` - 获取当前所有PID参数
- `RESET` - 重置所有PID参数为默认值

## 使用方法

### 1. 串口连接
- 波特率: 根据您的系统配置
- 数据位: 8
- 停止位: 1
- 校验位: 无

### 2. 命令格式
每个命令以换行符(\n)或回车符(\r)结束

### 3. 使用示例
```
LKP:0.5     // 设置位置环Kp为0.5
VKP:0.4     // 设置速度环Kp为0.4
GET         // 查看当前参数
RESET       // 重置为默认值
```

### 4. 响应示例
```
Location_Kp set to: 0.500
Velocity_Kp set to: 0.400

=== Current PID Parameters ===
Location Ring:
  Kp: 0.500
  Ki: 0.020
  Kd: 0.050
Velocity Ring:
  Kp: 0.400
  Ki: 0.030
  Kd: 0.080
==============================
```

## 调试建议

### 1. 参数调整顺序
1. 先调整位置环Kp，观察系统响应
2. 再调整速度环Kp，优化动态性能
3. 最后微调Ki和Kd参数

### 2. 参数范围建议
- **位置环Kp**: 0.1-1.0 (从小开始调整)
- **位置环Ki**: 0.01-0.1 (积分项要小)
- **位置环Kd**: 0.01-0.2 (微分项适中)
- **速度环Kp**: 0.2-0.8 (比位置环稍大)
- **速度环Ki**: 0.02-0.08 (积分项要小)
- **速度环Kd**: 0.05-0.15 (微分项适中)

### 3. 调试步骤
1. 使用`GET`命令查看当前参数
2. 逐步调整单个参数，观察效果
3. 记录最佳参数组合
4. 使用`RESET`命令可快速恢复默认值

## 安全特性
- 参数范围限制，防止设置过大值导致系统不稳定
- 实时生效，无需重启系统
- 支持参数重置，快速恢复默认配置
- 错误提示，帮助识别无效命令

## 注意事项
1. 调试时建议小幅度调整参数
2. 观察系统响应后再进行下一步调整
3. 记录有效的参数组合
4. 如果系统出现异常，立即使用`RESET`命令恢复
