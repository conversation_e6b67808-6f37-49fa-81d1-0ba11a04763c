/* 头文件声明区 */
#include "Task1.h"

/* 变量声明区 */

/* 数据变量 */

/* 任务1主体函数 */
uint8_t Task_1(void)
{
	static uint8_t Task_1_Flag = 0;     //任务1执行标志位
	static uint8_t Task_1_Step[8] = {0};//任务1步骤执行标志位 0-未执行 1-执行完毕；数组大小由最大步骤数决定
	uint8_t Task_1_Step_Temp;           //任务1步骤数组或值
	
	/* 读取步骤函数 */
	Task_1_Step_Temp = (Task_1_Step[0]<<0) | (Task_1_Step[1]<<1) | (Task_1_Step[2]<<2) | (Task_1_Step[3]<<3) | 
										 (Task_1_Step[4]<<4) | (Task_1_Step[5]<<5) | (Task_1_Step[6]<<6) | (Task_1_Step[7]<<7);
	
	/* 任务1主体函数 */
	switch(Task_1_Flag)
	{
		case 0://直走到5号位,拿完,放好
			switch(Task_1_Step_Temp)
			{
				case 0x00:
					/* 给定停止时间 */
					time=80;
					/* 直走固定距离 */
					Car_Tracking(30);
					/* 跳转下一步 */
					Task_1_Step[0] = 1;
				break;
				
				case 0x01:
					/* 到指定位置停车 */
					Task_1_Step[1] = Flag.Stop_Car?1:0;
					/* 执行下一步 */
					if(Task_1_Step[1] == 1) 
					{
                        /* 打断点 */
					    Task_1_Flag = 1;
					// 	/* 舵机变量赋值 */
					// 	Servo__Angle = 5;
					// 	/* 舵机执行函数 */
					// 	Servor_Proc();
						
					// 	/* 给定停止时间 */
					// 	time=400;
					// 	/* 直走固定距离 */
					// 	Car_Tracking(25);
					}
				break;
				
				case 0x03:
					// /* 到指定位置停车 */
					// Task_1_Step[2] = Flag.Stop_Car?1:0;
					// if(Task_1_Step[2] == 1)
					// {
					// 	/* 舵机变量赋值 */
					// 	Servo__Angle = 135;
					// 	/* 舵机执行函数 */
					// 	Servor_Proc();
						
					// 	/* 给定后退时间 */
					// 	Back_time=150;
					// 	/* 后退固定距离 */
					// 	Car_Back(55);
					// }
				break;
				
				case 0x07:
					// /* 到指定位置停车 */
					// Task_1_Step[3] = Flag.Stop_Car?1:0;
					// if(Task_1_Step[3] == 1)
					// {
					// 	/* 舵机变量赋值 */
					// 	Servo__Angle = 83;
					// 	/* 舵机执行函数 */
					// 	Servor_Proc();
					// }
				break;
				
				case 0x0f:
					/* 打断点 */
					Task_1_Flag = 1;
				break;
			}
			
		  /* 步骤重置函数 */
			if(Task_1_Flag == 1) 
			{
				memset(Task_1_Step,0,8);   //步骤重置函数
				return 1;
			}
		break;
		
		case 1://前往放料区
			switch(Task_1_Step_Temp)
			{
				case 0x00:
					
				break;
				
				case 0x01:
					
				break;
				
				case 0x03:
					
				break;
				
				case 0x07:
					
				break;
				
				case 0x0f:
					
				break;
				
				case 0x1f:
					/* 打断点 */
					Task_1_Flag = 2;
				break;
			}
			
			/* 步骤重置函数 */
			if(Task_1_Flag == 2) memset(Task_1_Step,0,8);   //步骤重置函数
		break;
			
		case 2://放物料
			switch(Task_1_Step_Temp)
			{
				case 0x00:
					/* 跳过点 */
					Task_1_Step[0] = 1;
				break;
				
				case 0x01:
					
				break;
				
				case 0x03:
					
				break;
				
				case 0x07:
					
				break;
				
				case 0x0f:
					/* 打断点 */
					Task_1_Flag = 3;
				break;
			}
			
		  /* 步骤重置函数 */
			if(Task_1_Flag == 3) memset(Task_1_Step,0,8);   //步骤重置函数
		break;
			
		case 3://回到出发区
			switch(Task_1_Step_Temp)
			{
				case 0x00:
					
				break;
				
				case 0x01:
					/* 打断点 */
					Task_1_Flag = 4;
				break;
			}
			
			/* 步骤重置函数 */
			if(Task_1_Flag == 4)
			{
				memset(Task_1_Step,0,8);   //步骤重置函数
				return 1;
			}
		break;
	}
	return 0;
}