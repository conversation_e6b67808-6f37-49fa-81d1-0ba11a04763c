******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 23 14:33:31 2025

OUTPUT FILE NAME:   <TI_CAR copy.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00007ae9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00009a38  000165c8  R  X
  SRAM                  20200000   00008000  000006d1  0000792f  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00009a38   00009a38    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00008680   00008680    r-x .text
  00008740    00008740    000012b0   000012b0    r-- .rodata
  000099f0    000099f0    00000048   00000048    r-- .cinit
20200000    20200000    000004d3   00000000    rw-
  20200000    20200000    0000044e   00000000    rw- .bss
  20200450    20200450    00000083   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00008680     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000410     Serial.o (.text.VOFA_ProcessCommand)
                  00000ea0    000003b8     libc.a : strtod.c.obj (.text.strtod)
                  00001258    00000364            : e_asin.c.obj (.text.asin)
                  000015bc    000002f8            : s_atan.c.obj (.text.atan)
                  000018b4    000002e8     Task1.o (.text.Task_1)
                  00001b9c    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001e14    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000204c    0000022c     MPU6050.o (.text.Read_Quad)
                  00002278    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  000024a4    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  000026c4    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  000028b8    000001ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00002aa4    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00002c80    000001b0     Task.o (.text.Task_Start)
                  00002e30    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002fd0    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00003162    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00003164    0000018c     Task_App.o (.text.Task_PID)
                  000032f0    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00003478    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  000035f0    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00003760    00000144     MPU6050.o (.text.MPU6050_Init)
                  000038a4    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000039e0    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00003b14    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00003c48    00000130     PID_Param.o (.text.VelocityRing_PID_Realize)
                  00003d78    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  00003ea8    00000128     inv_mpu.o (.text.mpu_init)
                  00003fd0    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  000040f4    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00004214    00000114     Interrupt.o (.text.GROUP1_IRQHandler)
                  00004328    00000110     OLED.o (.text.OLED_Init)
                  00004438    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00004544    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  0000464c    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00004750    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00004850    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  0000493c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00004a20    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  00004b04    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00004be0    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00004cb8    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00004d90    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004e64    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00004f34    000000cc     Motor.o (.text.read_encoder)
                  00005000    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  000050c4    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00005188    000000c0     PID_Param.o (.text.LocationRing_PID_Realize)
                  00005248    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00005304    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  000053bc    000000b4     Task.o (.text.Task_Add)
                  00005470    000000b4     Serial.o (.text.VOFA_SendPIDParams)
                  00005524    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  000055d0    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  0000567c    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  0000571e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00005720    000000a0     Task_App.o (.text.Task_Serial)
                  000057c0    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  0000585c    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  000058f4    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  0000598c    00000096     MPU6050.o (.text.inv_row_2_scale)
                  00005a22    00000002     --HOLE-- [fill = 0]
                  00005a24    00000094     MyConfig.o (.text.Car_Tracking)
                  00005ab8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  00005b44    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00005bd0    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00005c5c    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00005ce8    00000084     Motor.o (.text.Load_Motor_PWM)
                  00005d6c    00000084     Serial.o (.text.MyPrintf)
                  00005df0    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00005e74    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00005ef8    00000084     main.o (.text.main)
                  00005f7c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00005ffe    00000002     --HOLE-- [fill = 0]
                  00006000    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000607c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000060f0    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00006164    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  000061d8    00000070     Motor.o (.text.Motor_SetPWM)
                  00006248    00000070     Task.o (.text.Task_GetMaxUsed)
                  000062b8    0000006c     MyConfig.o (.text.LocationRing_Out)
                  00006324    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  0000638e    00000002     --HOLE-- [fill = 0]
                  00006390    00000068     Task_App.o (.text.Task_Init)
                  000063f8    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00006460    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  000064c6    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  0000652c    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00006590    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000065f4    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00006656    00000002     --HOLE-- [fill = 0]
                  00006658    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000066ba    00000002     --HOLE-- [fill = 0]
                  000066bc    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  0000671c    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  0000677c    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  000067dc    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  0000683c    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000689a    00000002     --HOLE-- [fill = 0]
                  0000689c    0000005c     Motor.o (.text.Motor_SetDirc)
                  000068f8    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00006954    0000005c     Task_App.o (.text.Task_Encoder)
                  000069b0    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00006a0c    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  00006a68    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00006ac4    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00006b1c    00000058     Serial.o (.text.Serial_Init)
                  00006b74    00000058     Task_App.o (.text.Task_IdleFunction)
                  00006bcc    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00006c24    00000058            : _printfi.c.obj (.text._pconv_f)
                  00006c7c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00006cd2    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00006d24    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00006d74    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00006dc4    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00006e14    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00006e60    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00006eac    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00006ef6    00000002     --HOLE-- [fill = 0]
                  00006ef8    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00006f40    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00006f88    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  00006fd0    00000048     OLED.o (.text.mspm0_i2c_disable)
                  00007018    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  0000705c    00000044     PID_Param.o (.text.PID_Param_Init)
                  000070a0    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  000070e4    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00007128    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  0000716c    00000040     MyConfig.o (.text.VelocityRing_Out)
                  000071ac    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000071ec    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  0000722c    00000040     libc.a : atoi.c.obj (.text.atoi)
                  0000726c    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  000072ac    0000003e     Task.o (.text.Task_CMP)
                  000072ea    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  00007328    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00007364    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000073a0    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000073dc    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00007418    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00007454    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00007490    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000074cc    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00007508    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00007542    00000002     --HOLE-- [fill = 0]
                  00007544    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0000757e    00000002     --HOLE-- [fill = 0]
                  00007580    00000038     Motor.o (.text.Motor_Start)
                  000075b8    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  000075f0    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00007624    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00007658    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000768c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000076c0    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  000076f4    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  00007726    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00007758    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00007788    00000030     Interrupt.o (.text.Interrupt_Init)
                  000077b8    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  000077e8    00000030     Interrupt.o (.text.SysTick_Handler)
                  00007818    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00007848    00000030            : vsnprintf.c.obj (.text._outs)
                  00007878    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  000078a8    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  000078d8    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00007904    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00007930    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00007958    00000028     OLED.o (.text.DL_Common_updateReg)
                  00007980    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000079a8    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  000079d0    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  000079f8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00007a20    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00007a48    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00007a70    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00007a98    00000028     SysTick.o (.text.SysTick_Increasment)
                  00007ac0    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00007ae8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00007b10    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00007b36    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00007b5c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00007b82    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00007ba8    00000024     Serial.o (.text.DL_DMA_getTransferSize)
                  00007bcc    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00007bf0    00000024     MyConfig.o (.text.LocationRing_VelocityRing_Control)
                  00007c14    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00007c38    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00007c5a    00000022            : strncmp.c.obj (.text.strncmp)
                  00007c7c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00007c9c    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007cbc    00000020     driverlib.a : dl_uart.o (.text.DL_UART_transmitDataBlocking)
                  00007cdc    00000020     SysTick.o (.text.Delay)
                  00007cfc    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00007d1c    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00007d3a    00000002     --HOLE-- [fill = 0]
                  00007d3c    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00007d5a    00000002     --HOLE-- [fill = 0]
                  00007d5c    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00007d78    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00007d94    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00007db0    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00007dcc    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00007de8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00007e04    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00007e20    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00007e3c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00007e58    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00007e74    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  00007e90    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00007eac    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00007ec8    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00007ee4    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00007f00    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00007f1c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00007f38    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00007f54    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00007f70    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00007f8c    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00007fa4    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00007fbc    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00007fd4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00007fec    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00008004    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  0000801c    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00008034    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  0000804c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00008064    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  0000807c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00008094    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  000080ac    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000080c4    00000018     OLED.o (.text.DL_GPIO_setPins)
                  000080dc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000080f4    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  0000810c    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00008124    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  0000813c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00008154    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  0000816c    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00008184    00000018     OLED.o (.text.DL_I2C_enablePower)
                  0000819c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000081b4    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  000081cc    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  000081e4    00000018     MPU6050.o (.text.DL_I2C_reset)
                  000081fc    00000018     OLED.o (.text.DL_I2C_reset)
                  00008214    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  0000822c    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00008244    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  0000825c    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00008274    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  0000828c    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000082a4    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000082bc    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000082d4    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000082ec    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00008304    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  0000831c    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00008334    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  0000834c    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00008364    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  0000837c    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00008394    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000083aa    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  000083c0    00000016     OLED.o (.text.DL_GPIO_readPins)
                  000083d6    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  000083ec    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00008402    00000016     SysTick.o (.text.SysGetTick)
                  00008418    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  0000842c    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00008440    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00008454    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00008468    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  0000847c    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00008490    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  000084a4    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  000084b8    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000084cc    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000084e0    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000084f4    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00008508    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  0000851c    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00008530    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00008544    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00008556    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00008568    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000857a    00000002     --HOLE-- [fill = 0]
                  0000857c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  0000858c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  0000859c    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000085ac    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000085bc    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  000085cc    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000085da    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000085e8    0000000e     MPU6050.o (.text.tap_cb)
                  000085f6    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00008604    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00008610    0000000c     SysTick.o (.text.Sys_GetTick)
                  0000861c    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00008628    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00008632    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000863c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  0000864c    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00008656    00000002     --HOLE-- [fill = 0]
                  00008658    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00008668    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00008672    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000867c    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008686    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00008690    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  000086a0    0000000a     MPU6050.o (.text.android_orient_cb)
                  000086aa    0000000a     libc.a : atof.c.obj (.text.atof)
                  000086b4    00000008            : strtod.c.obj (.text.OUTLINED_FUNCTION_0)
                  000086bc    00000008            : strtod.c.obj (.text.OUTLINED_FUNCTION_1)
                  000086c4    00000010     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.tramp.__aeabi_dcmpeq.1)
                  000086d4    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  000086dc    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000086e4    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000086ec    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  000086f2    00000002     --HOLE-- [fill = 0]
                  000086f4    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00008704    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  0000870a    00000006            : exit.c.obj (.text:abort)
                  00008710    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00008714    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00008718    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  0000871c    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00008720    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00008730    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00008734    0000000c     --HOLE-- [fill = 0]

.cinit     0    000099f0    00000048     
                  000099f0    0000001e     (.cinit..data.load) [load image, compression = lzss]
                  00009a0e    00000002     --HOLE-- [fill = 0]
                  00009a10    0000000c     (__TI_handler_table)
                  00009a1c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00009a24    00000010     (__TI_cinit_table)
                  00009a34    00000004     --HOLE-- [fill = 0]

.rodata    0    00008740    000012b0     
                  00008740    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00009336    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00009340    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00009441    00000007     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00009448    00000080     libc.a : strtod.c.obj (.rodata.digits)
                  000094c8    00000048            : strtod.c.obj (.rodata.powerof10)
                  00009510    00000040            : s_atan.c.obj (.rodata.cst32)
                  00009550    00000029     Serial.o (.rodata.str1.11786478787035890685.1)
                  00009579    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  0000957c    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000095a4    00000028     inv_mpu.o (.rodata.test)
                  000095cc    00000027     Serial.o (.rodata.str1.10634499133543917963.1)
                  000095f3    00000027     Serial.o (.rodata.str1.8061444200610707353.1)
                  0000961a    00000026     Serial.o (.rodata.str1.11190010631395540378.1)
                  00009640    00000026     Serial.o (.rodata.str1.1560654190226076289.1)
                  00009666    00000026     Serial.o (.rodata.str1.5683847496476334445.1)
                  0000968c    00000026     Serial.o (.rodata.str1.8790017304546924836.1)
                  000096b2    00000021     Serial.o (.rodata.str1.7115817706510113265.1)
                  000096d3    00000021     Serial.o (.rodata.str1.7326805063803889745.1)
                  000096f4    0000001f     Serial.o (.rodata.str1.7150362006028252639.1)
                  00009713    0000001e     inv_mpu.o (.rodata.reg)
                  00009731    0000001e     Serial.o (.rodata.str1.2026234543345316402.1)
                  0000974f    0000001e     Serial.o (.rodata.str1.3317145043284469420.1)
                  0000976d    0000001e     Serial.o (.rodata.str1.7234419232742107752.1)
                  0000978b    0000001e     Serial.o (.rodata.str1.7358502917545056261.1)
                  000097a9    0000001e     Serial.o (.rodata.str1.7850966677388071438.1)
                  000097c7    0000001e     Serial.o (.rodata.str1.9578234284467481337.1)
                  000097e5    0000001b     Serial.o (.rodata.str1.12717330112019715612.1)
                  00009800    0000001b     Serial.o (.rodata.str1.13287575277769732569.1)
                  0000981b    0000001b     Serial.o (.rodata.str1.17063193913729639070.1)
                  00009836    0000001b     Serial.o (.rodata.str1.17926260741898151649.1)
                  00009851    0000001b     Serial.o (.rodata.str1.18110974824152265008.1)
                  0000986c    0000001b     Serial.o (.rodata.str1.5732366753199515285.1)
                  00009887    0000001b     Serial.o (.rodata.str1.7430549043758673718.1)
                  000098a2    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  000098a4    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  000098bc    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  000098d4    00000017     Task_App.o (.rodata.str1.5883415095785080416.1)
                  000098eb    00000016     Serial.o (.rodata.str1.14518273289884796303.1)
                  00009901    00000016     Serial.o (.rodata.str1.499443011072999736.1)
                  00009917    00000013     Task_App.o (.rodata.str1.14074990341397557290.1)
                  0000992a    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  0000993b    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  0000994c    00000011     Serial.o (.rodata.str1.17043161187319707899.1)
                  0000995d    00000011     Serial.o (.rodata.str1.9904979458565590603.1)
                  0000996e    0000000d     Serial.o (.rodata.str1.10356573403970012420.1)
                  0000997b    0000000d     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00009988    0000000d     Serial.o (.rodata.str1.17717440993058737695.1)
                  00009995    0000000d     Serial.o (.rodata.str1.8476430949555505974.1)
                  000099a2    0000000c     inv_mpu.o (.rodata.hw)
                  000099ae    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000099b0    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  000099b8    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  000099c0    00000006     Serial.o (.rodata.str1.11876777600748611757.1)
                  000099c6    00000005     Serial.o (.rodata.str1.10176885038890850391.1)
                  000099cb    00000005     Serial.o (.rodata.str1.10304492783397124228.1)
                  000099d0    00000005     Serial.o (.rodata.str1.13819545776547716190.1)
                  000099d5    00000005     Serial.o (.rodata.str1.3218052037662060792.1)
                  000099da    00000005     Serial.o (.rodata.str1.5772894128764667447.1)
                  000099df    00000005     Serial.o (.rodata.str1.8543202960346806002.1)
                  000099e4    00000004     Serial.o (.rodata.str1.16956771667979876701.1)
                  000099e8    00000004     Task_App.o (.rodata.str1.3743034515018940988.1)
                  000099ec    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  000099ee    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000044e     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000bc     (.common:gMotorFrontBackup)
                  202003ac    00000048     (.common:PID)
                  202003f4    00000018     (.common:Param)
                  2020040c    00000010     (.common:quat)
                  2020041c    0000000b     (.common:Flag)
                  20200427    00000001     (.common:more)
                  20200428    00000006     (.common:Data_Accel)
                  2020042e    00000006     (.common:Data_Gyro)
                  20200434    00000004     (.common:Data_Pitch)
                  20200438    00000004     (.common:Data_Roll)
                  2020043c    00000004     (.common:Data_Yaw)
                  20200440    00000004     (.common:ExISR_Flag)
                  20200444    00000004     (.common:sensor_timestamp)
                  20200448    00000002     (.common:sensors)
                  2020044a    00000002     (.common:stop_time_cnt)
                  2020044c    00000002     (.common:time)

.data      0    20200450    00000083     UNINITIALIZED
                  20200450    0000002c     inv_mpu.o (.data.st)
                  2020047c    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  2020048c    0000000e     MPU6050.o (.data.hal)
                  2020049a    00000009     MPU6050.o (.data.gyro_orientation)
                  202004a3    00000008     Task1.o (.data.Task_1.Task_1_Step)
                  202004ab    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  202004ac    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004b0    00000004     Interrupt.o (.data.GROUP1_IRQHandler.left_last_time)
                  202004b4    00000004     Interrupt.o (.data.GROUP1_IRQHandler.right_last_time)
                  202004b8    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004bc    00000004     SysTick.o (.data.delayTick)
                  202004c0    00000004     Motor.o (.data.read_encoder.Data_MotorEncoder_Old)
                  202004c4    00000004     SysTick.o (.data.uwTick)
                  202004c8    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004ca    00000002     Serial.o (.data.rx_len)
                  202004cc    00000001     Task1.o (.data.Task_1.Task_1_Flag)
                  202004cd    00000001     Key_Led.o (.data.Task_Flag)
                  202004ce    00000001     Task.o (.data.Task_Num)
                  202004cf    00000001     Task_App.o (.data.Task_Serial.monitor_counter)
                  202004d0    00000001     Key_Led.o (.data.Task_State)
                  202004d1    00000001     Interrupt.o (.data.enable_group1_irq)
                  202004d2    00000001     Serial.o (.data.vofa_cmd_ready)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3134    115       188    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         132     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3274    307       188    
                                                               
    .\APP\Src\
       Task_App.o                     840     74        7      
       Interrupt.o                    518     0         14     
    +--+------------------------------+-------+---------+---------+
       Total:                         1358    74        21     
                                                               
    .\BSP\Src\
       Serial.o                       1680    894       515    
       MPU6050.o                      2464    0         70     
       OLED.o                         1308    0         0      
       Task.o                         786     0         241    
       Task1.o                        744     0         9      
       Motor.o                        664     0         4      
       PID_Param.o                    564     0         72     
       MyConfig.o                     356     0         39     
       SysTick.o                      106     0         8      
       Key_Led.o                      0       0         2      
    +--+------------------------------+-------+---------+---------+
       Total:                         8672    894       960    
                                                               
    .\DMP\
       inv_mpu_dmp_motion_driver.o    3110    3062      16     
       inv_mpu.o                      4600    82        44     
    +--+------------------------------+-------+---------+---------+
       Total:                         7710    3144      60     
                                                               
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      122     0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1144    0         0      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       strtod.c.obj                   968     200       0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       vsnprintf.c.obj                136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       strncmp.c.obj                  34      0         0      
       memcmp.c.obj                   32      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       atof.c.obj                     10      0         0      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         9284    555       4      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               114     0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2946    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       66        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   34392   5040      1745   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00009a24 records: 2, size/record: 8, table size: 16
	.data: load addr=000099f0, load size=0000001e bytes, run addr=20200450, run size=00000083 bytes, compression=lzss
	.bss: load addr=00009a1c, load size=00000008 bytes, run addr=20200000, run size=0000044e bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00009a10 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002fd1     0000863c     0000863a   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   0000493d     00008658     00008654   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00008670          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00008684          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             000086da          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00008708          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00004439     00008690     0000868e   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dcmpeq            $Tramp$TT$L$PI$$__aeabi_dcmpeq
   000065f5     000086c4     000086c2   libc.a : strtod.c.obj (.text.OUTLINED_FUNCTION_1)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00002fdb     000086f4     000086f0   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             0000871a          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00007ae9     00008720     0000871c   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[6 trampolines]
[11 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00008711  ADC0_IRQHandler                      
00008711  ADC1_IRQHandler                      
00008711  AES_IRQHandler                       
00008714  C$$EXIT                              
00008711  CANFD0_IRQHandler                    
00005a25  Car_Tracking                         
00008711  DAC0_IRQHandler                      
00008629  DL_Common_delayCycles                
00006e15  DL_DMA_initChannel                   
0000683d  DL_I2C_fillControllerTXFIFO          
000073dd  DL_I2C_flushControllerTXFIFO         
00007b83  DL_I2C_setClockConfig                
00004b05  DL_SYSCTL_configSYSPLL               
0000652d  DL_SYSCTL_setHFCLKSourceHFXTParams   
00007019  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000464d  DL_Timer_initFourCCPWMMode           
00007f1d  DL_Timer_setCaptCompUpdateMethod     
000082bd  DL_Timer_setCaptureCompareOutCtl     
0000858d  DL_Timer_setCaptureCompareValue      
00007f39  DL_Timer_setClockConfig              
00006ef9  DL_UART_init                         
00008545  DL_UART_setClockConfig               
00007cbd  DL_UART_transmitDataBlocking         
00008711  DMA_IRQHandler                       
20200428  Data_Accel                           
2020042e  Data_Gyro                            
202004ac  Data_MotorEncoder                    
20200434  Data_Pitch                           
20200438  Data_Roll                            
2020043c  Data_Yaw                             
00008711  Default_Handler                      
00007cdd  Delay                                
20200440  ExISR_Flag                           
2020041c  Flag                                 
202004ab  Flag_MPU6050_Ready                   
00008711  GROUP0_IRQHandler                    
00004215  GROUP1_IRQHandler                    
00008715  HOSTexit                             
00008711  HardFault_Handler                    
00008711  I2C0_IRQHandler                      
00008711  I2C1_IRQHandler                      
00006325  I2C_OLED_Clear                       
0000585d  I2C_OLED_WR_Byte                     
000066bd  I2C_OLED_i2c_sda_unlock              
00007789  Interrupt_Init                       
00005ce9  Load_Motor_PWM                       
000062b9  LocationRing_Out                     
00005189  LocationRing_PID_Realize             
00007bf1  LocationRing_VelocityRing_Control    
00003761  MPU6050_Init                         
0000689d  Motor_SetDirc                        
000061d9  Motor_SetPWM                         
00007581  Motor_Start                          
00005d6d  MyPrintf                             
00008711  NMI_Handler                          
00004329  OLED_Init                            
202003ac  PID                                  
0000705d  PID_Param_Init                       
202003f4  Param                                
00008711  PendSV_Handler                       
00008711  RTC_IRQHandler                       
0000204d  Read_Quad                            
0000871d  Reset_Handler                        
00008711  SPI0_IRQHandler                      
00008711  SPI1_IRQHandler                      
00008711  SVC_Handler                          
000077b9  SYSCFG_DL_DMA_CH_RX_init             
00008365  SYSCFG_DL_DMA_CH_TX_init             
00008605  SYSCFG_DL_DMA_init                   
000028b9  SYSCFG_DL_GPIO_init                  
00006ac5  SYSCFG_DL_I2C_MPU6050_init           
00006591  SYSCFG_DL_I2C_OLED_init              
00005ab9  SYSCFG_DL_MotorFront_init            
000068f9  SYSCFG_DL_SYSCTL_init                
0000859d  SYSCFG_DL_SYSTICK_init               
00005df1  SYSCFG_DL_UART0_init                 
0000768d  SYSCFG_DL_init                       
00005b45  SYSCFG_DL_initPower                  
00006b1d  Serial_Init                          
20200000  Serial_RxData                        
00008403  SysGetTick                           
000077e9  SysTick_Handler                      
00007a99  SysTick_Increasment                  
00008611  Sys_GetTick                          
00008711  TIMA0_IRQHandler                     
00008711  TIMA1_IRQHandler                     
00008711  TIMG0_IRQHandler                     
00008711  TIMG12_IRQHandler                    
00008711  TIMG6_IRQHandler                     
00008711  TIMG7_IRQHandler                     
00008711  TIMG8_IRQHandler                     
00008557  TI_memcpy_small                      
000085f7  TI_memset_small                      
000018b5  Task_1                               
000053bd  Task_Add                             
00006955  Task_Encoder                         
202004cd  Task_Flag                            
00006249  Task_GetMaxUsed                      
00006b75  Task_IdleFunction                    
00006391  Task_Init                            
00003165  Task_PID                             
00005721  Task_Serial                          
00002c81  Task_Start                           
202004d0  Task_State                           
00008711  UART0_IRQHandler                     
00008711  UART1_IRQHandler                     
00008711  UART2_IRQHandler                     
00008711  UART3_IRQHandler                     
00000a91  VOFA_ProcessCommand                  
00005471  VOFA_SendPIDParams                   
0000716d  VelocityRing_Out                     
00003c49  VelocityRing_PID_Realize             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00009a24  __TI_CINIT_Base                      
00009a34  __TI_CINIT_Limit                     
00009a34  __TI_CINIT_Warm                      
00009a10  __TI_Handler_Table_Base              
00009a1c  __TI_Handler_Table_Limit             
000074cd  __TI_auto_init_nobinit_nopinit       
00006001  __TI_decompress_lzss                 
00008569  __TI_decompress_none                 
00006bcd  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000085bd  __TI_zero_init                       
00002fdb  __adddf3                             
00004cc3  __addsf3                             
00009340  __aeabi_ctype_table_                 
00009340  __aeabi_ctype_table_C                
000060f1  __aeabi_d2f                          
00006ead  __aeabi_d2iz                         
00002fdb  __aeabi_dadd                         
000065f5  __aeabi_dcmpeq                       
00006631  __aeabi_dcmpge                       
00006645  __aeabi_dcmpgt                       
0000661d  __aeabi_dcmple                       
00006609  __aeabi_dcmplt                       
00004439  __aeabi_ddiv                         
0000493d  __aeabi_dmul                         
00002fd1  __aeabi_dsub                         
202004b8  __aeabi_errno                        
000086dd  __aeabi_errno_addr                   
000071ed  __aeabi_f2d                          
000075b9  __aeabi_f2iz                         
00004cc3  __aeabi_fadd                         
00006659  __aeabi_fcmpeq                       
00006695  __aeabi_fcmpge                       
000066a9  __aeabi_fcmpgt                       
00006681  __aeabi_fcmple                       
0000666d  __aeabi_fcmplt                       
00005f7d  __aeabi_fdiv                         
00005bd1  __aeabi_fmul                         
00004cb9  __aeabi_fsub                         
00007905  __aeabi_i2d                          
00007455  __aeabi_i2f                          
00006c7d  __aeabi_idiv                         
00003163  __aeabi_idiv0                        
00006c7d  __aeabi_idivmod                      
0000571f  __aeabi_ldiv0                        
00007d3d  __aeabi_llsl                         
00007c15  __aeabi_lmul                         
0000861d  __aeabi_memclr                       
0000861d  __aeabi_memclr4                      
0000861d  __aeabi_memclr8                      
000086e5  __aeabi_memcpy                       
000086e5  __aeabi_memcpy4                      
000086e5  __aeabi_memcpy8                      
000085cd  __aeabi_memset                       
000085cd  __aeabi_memset4                      
000085cd  __aeabi_memset8                      
00007ac1  __aeabi_ui2f                         
000071ad  __aeabi_uidiv                        
000071ad  __aeabi_uidivmod                     
000084f5  __aeabi_uldivmod                     
00007d3d  __ashldi3                            
ffffffff  __binit__                            
000063f9  __cmpdf2                             
00007509  __cmpsf2                             
00004439  __divdf3                             
00005f7d  __divsf3                             
000063f9  __eqdf2                              
00007509  __eqsf2                              
000071ed  __extendsfdf2                        
00006ead  __fixdfsi                            
000075b9  __fixsfsi                            
00007905  __floatsidf                          
00007455  __floatsisf                          
00007ac1  __floatunsisf                        
0000607d  __gedf2                              
00007491  __gesf2                              
0000607d  __gtdf2                              
00007491  __gtsf2                              
000063f9  __ledf2                              
00007509  __lesf2                              
000063f9  __ltdf2                              
00007509  __ltsf2                              
UNDEFED   __mpu_init                           
0000493d  __muldf3                             
00007c15  __muldi3                             
00007545  __muldsi3                            
00005bd1  __mulsf3                             
000063f9  __nedf2                              
00007509  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002fd1  __subdf3                             
00004cb9  __subsf3                             
000060f1  __truncdfsf2                         
0000567d  __udivmoddi4                         
00007ae9  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00008731  _system_pre_init                     
0000870b  abort                                
00001259  asin                                 
00001259  asinl                                
000015bd  atan                                 
000032f1  atan2                                
000032f1  atan2l                               
000015bd  atanl                                
000086ab  atof                                 
0000722d  atoi                                 
ffffffff  binit                                
202004bc  delayTick                            
00006f41  dmp_enable_6x_lp_quat                
00001b9d  dmp_enable_feature                   
0000671d  dmp_enable_gyro_cal                  
00006f89  dmp_enable_lp_quat                   
00007f71  dmp_load_motion_driver_firmware      
000026c5  dmp_read_fifo                        
00008509  dmp_register_android_orient_cb       
0000851d  dmp_register_tap_cb                  
000058f5  dmp_set_fifo_rate                    
00003479  dmp_set_orientation                  
000070a1  dmp_set_shake_reject_thresh          
000076f5  dmp_set_shake_reject_time            
00007727  dmp_set_shake_reject_timeout         
000064c7  dmp_set_tap_axes                     
000070e5  dmp_set_tap_count                    
00001e15  dmp_set_tap_thresh                   
00007879  dmp_set_tap_time                     
000078a9  dmp_set_tap_time_multi               
202004d1  enable_group1_irq                    
000069b1  frexp                                
000069b1  frexpl                               
202002f0  gMotorFrontBackup                    
000099a2  hw                                   
00000000  interruptVectors                     
00004be1  ldexp                                
00004be1  ldexpl                               
00005ef9  main                                 
00007c39  memccpy                              
00007cfd  memcmp                               
20200427  more                                 
0000677d  mpu6050_i2c_sda_unlock               
00005249  mpu_configure_fifo                   
00006165  mpu_get_accel_fsr                    
000067dd  mpu_get_gyro_fsr                     
000076c1  mpu_get_sample_rate                  
00003ea9  mpu_init                             
00003fd1  mpu_load_firmware                    
00004751  mpu_lp_accel_mode                    
00004545  mpu_read_fifo_stream                 
00005525  mpu_read_mem                         
00002279  mpu_reset_fifo                       
00004a21  mpu_set_accel_fsr                    
00002e31  mpu_set_bypass                       
00005305  mpu_set_dmp_state                    
00005001  mpu_set_gyro_fsr                     
000057c1  mpu_set_int_latched                  
00004e65  mpu_set_lpf                          
00004851  mpu_set_sample_rate                  
00003d79  mpu_set_sensors                      
000055d1  mpu_write_mem                        
000039e1  mspm0_i2c_read                       
000050c5  mspm0_i2c_write                      
00003b15  qsort                                
2020040c  quat                                 
00004f35  read_encoder                         
00009713  reg                                  
00004be1  scalbn                               
00004be1  scalbnl                              
20200444  sensor_timestamp                     
20200448  sensors                              
000035f1  sqrt                                 
000035f1  sqrtl                                
2020044a  stop_time_cnt                        
00007c5b  strncmp                              
00000ea1  strtod                               
00000ea1  strtold                              
000095a4  test                                 
2020044c  time                                 
202004c4  uwTick                               
0000726d  vsnprintf                            
000085ad  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  VOFA_ProcessCommand                  
00000ea1  strtod                               
00000ea1  strtold                              
00001259  asin                                 
00001259  asinl                                
000015bd  atan                                 
000015bd  atanl                                
000018b5  Task_1                               
00001b9d  dmp_enable_feature                   
00001e15  dmp_set_tap_thresh                   
0000204d  Read_Quad                            
00002279  mpu_reset_fifo                       
000026c5  dmp_read_fifo                        
000028b9  SYSCFG_DL_GPIO_init                  
00002c81  Task_Start                           
00002e31  mpu_set_bypass                       
00002fd1  __aeabi_dsub                         
00002fd1  __subdf3                             
00002fdb  __adddf3                             
00002fdb  __aeabi_dadd                         
00003163  __aeabi_idiv0                        
00003165  Task_PID                             
000032f1  atan2                                
000032f1  atan2l                               
00003479  dmp_set_orientation                  
000035f1  sqrt                                 
000035f1  sqrtl                                
00003761  MPU6050_Init                         
000039e1  mspm0_i2c_read                       
00003b15  qsort                                
00003c49  VelocityRing_PID_Realize             
00003d79  mpu_set_sensors                      
00003ea9  mpu_init                             
00003fd1  mpu_load_firmware                    
00004215  GROUP1_IRQHandler                    
00004329  OLED_Init                            
00004439  __aeabi_ddiv                         
00004439  __divdf3                             
00004545  mpu_read_fifo_stream                 
0000464d  DL_Timer_initFourCCPWMMode           
00004751  mpu_lp_accel_mode                    
00004851  mpu_set_sample_rate                  
0000493d  __aeabi_dmul                         
0000493d  __muldf3                             
00004a21  mpu_set_accel_fsr                    
00004b05  DL_SYSCTL_configSYSPLL               
00004be1  ldexp                                
00004be1  ldexpl                               
00004be1  scalbn                               
00004be1  scalbnl                              
00004cb9  __aeabi_fsub                         
00004cb9  __subsf3                             
00004cc3  __addsf3                             
00004cc3  __aeabi_fadd                         
00004e65  mpu_set_lpf                          
00004f35  read_encoder                         
00005001  mpu_set_gyro_fsr                     
000050c5  mspm0_i2c_write                      
00005189  LocationRing_PID_Realize             
00005249  mpu_configure_fifo                   
00005305  mpu_set_dmp_state                    
000053bd  Task_Add                             
00005471  VOFA_SendPIDParams                   
00005525  mpu_read_mem                         
000055d1  mpu_write_mem                        
0000567d  __udivmoddi4                         
0000571f  __aeabi_ldiv0                        
00005721  Task_Serial                          
000057c1  mpu_set_int_latched                  
0000585d  I2C_OLED_WR_Byte                     
000058f5  dmp_set_fifo_rate                    
00005a25  Car_Tracking                         
00005ab9  SYSCFG_DL_MotorFront_init            
00005b45  SYSCFG_DL_initPower                  
00005bd1  __aeabi_fmul                         
00005bd1  __mulsf3                             
00005ce9  Load_Motor_PWM                       
00005d6d  MyPrintf                             
00005df1  SYSCFG_DL_UART0_init                 
00005ef9  main                                 
00005f7d  __aeabi_fdiv                         
00005f7d  __divsf3                             
00006001  __TI_decompress_lzss                 
0000607d  __gedf2                              
0000607d  __gtdf2                              
000060f1  __aeabi_d2f                          
000060f1  __truncdfsf2                         
00006165  mpu_get_accel_fsr                    
000061d9  Motor_SetPWM                         
00006249  Task_GetMaxUsed                      
000062b9  LocationRing_Out                     
00006325  I2C_OLED_Clear                       
00006391  Task_Init                            
000063f9  __cmpdf2                             
000063f9  __eqdf2                              
000063f9  __ledf2                              
000063f9  __ltdf2                              
000063f9  __nedf2                              
000064c7  dmp_set_tap_axes                     
0000652d  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006591  SYSCFG_DL_I2C_OLED_init              
000065f5  __aeabi_dcmpeq                       
00006609  __aeabi_dcmplt                       
0000661d  __aeabi_dcmple                       
00006631  __aeabi_dcmpge                       
00006645  __aeabi_dcmpgt                       
00006659  __aeabi_fcmpeq                       
0000666d  __aeabi_fcmplt                       
00006681  __aeabi_fcmple                       
00006695  __aeabi_fcmpge                       
000066a9  __aeabi_fcmpgt                       
000066bd  I2C_OLED_i2c_sda_unlock              
0000671d  dmp_enable_gyro_cal                  
0000677d  mpu6050_i2c_sda_unlock               
000067dd  mpu_get_gyro_fsr                     
0000683d  DL_I2C_fillControllerTXFIFO          
0000689d  Motor_SetDirc                        
000068f9  SYSCFG_DL_SYSCTL_init                
00006955  Task_Encoder                         
000069b1  frexp                                
000069b1  frexpl                               
00006ac5  SYSCFG_DL_I2C_MPU6050_init           
00006b1d  Serial_Init                          
00006b75  Task_IdleFunction                    
00006bcd  __TI_ltoa                            
00006c7d  __aeabi_idiv                         
00006c7d  __aeabi_idivmod                      
00006e15  DL_DMA_initChannel                   
00006ead  __aeabi_d2iz                         
00006ead  __fixdfsi                            
00006ef9  DL_UART_init                         
00006f41  dmp_enable_6x_lp_quat                
00006f89  dmp_enable_lp_quat                   
00007019  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000705d  PID_Param_Init                       
000070a1  dmp_set_shake_reject_thresh          
000070e5  dmp_set_tap_count                    
0000716d  VelocityRing_Out                     
000071ad  __aeabi_uidiv                        
000071ad  __aeabi_uidivmod                     
000071ed  __aeabi_f2d                          
000071ed  __extendsfdf2                        
0000722d  atoi                                 
0000726d  vsnprintf                            
000073dd  DL_I2C_flushControllerTXFIFO         
00007455  __aeabi_i2f                          
00007455  __floatsisf                          
00007491  __gesf2                              
00007491  __gtsf2                              
000074cd  __TI_auto_init_nobinit_nopinit       
00007509  __cmpsf2                             
00007509  __eqsf2                              
00007509  __lesf2                              
00007509  __ltsf2                              
00007509  __nesf2                              
00007545  __muldsi3                            
00007581  Motor_Start                          
000075b9  __aeabi_f2iz                         
000075b9  __fixsfsi                            
0000768d  SYSCFG_DL_init                       
000076c1  mpu_get_sample_rate                  
000076f5  dmp_set_shake_reject_time            
00007727  dmp_set_shake_reject_timeout         
00007789  Interrupt_Init                       
000077b9  SYSCFG_DL_DMA_CH_RX_init             
000077e9  SysTick_Handler                      
00007879  dmp_set_tap_time                     
000078a9  dmp_set_tap_time_multi               
00007905  __aeabi_i2d                          
00007905  __floatsidf                          
00007a99  SysTick_Increasment                  
00007ac1  __aeabi_ui2f                         
00007ac1  __floatunsisf                        
00007ae9  _c_int00_noargs                      
00007b83  DL_I2C_setClockConfig                
00007bf1  LocationRing_VelocityRing_Control    
00007c15  __aeabi_lmul                         
00007c15  __muldi3                             
00007c39  memccpy                              
00007c5b  strncmp                              
00007cbd  DL_UART_transmitDataBlocking         
00007cdd  Delay                                
00007cfd  memcmp                               
00007d3d  __aeabi_llsl                         
00007d3d  __ashldi3                            
00007f1d  DL_Timer_setCaptCompUpdateMethod     
00007f39  DL_Timer_setClockConfig              
00007f71  dmp_load_motion_driver_firmware      
000082bd  DL_Timer_setCaptureCompareOutCtl     
00008365  SYSCFG_DL_DMA_CH_TX_init             
00008403  SysGetTick                           
000084f5  __aeabi_uldivmod                     
00008509  dmp_register_android_orient_cb       
0000851d  dmp_register_tap_cb                  
00008545  DL_UART_setClockConfig               
00008557  TI_memcpy_small                      
00008569  __TI_decompress_none                 
0000858d  DL_Timer_setCaptureCompareValue      
0000859d  SYSCFG_DL_SYSTICK_init               
000085ad  wcslen                               
000085bd  __TI_zero_init                       
000085cd  __aeabi_memset                       
000085cd  __aeabi_memset4                      
000085cd  __aeabi_memset8                      
000085f7  TI_memset_small                      
00008605  SYSCFG_DL_DMA_init                   
00008611  Sys_GetTick                          
0000861d  __aeabi_memclr                       
0000861d  __aeabi_memclr4                      
0000861d  __aeabi_memclr8                      
00008629  DL_Common_delayCycles                
000086ab  atof                                 
000086dd  __aeabi_errno_addr                   
000086e5  __aeabi_memcpy                       
000086e5  __aeabi_memcpy4                      
000086e5  __aeabi_memcpy8                      
0000870b  abort                                
00008711  ADC0_IRQHandler                      
00008711  ADC1_IRQHandler                      
00008711  AES_IRQHandler                       
00008711  CANFD0_IRQHandler                    
00008711  DAC0_IRQHandler                      
00008711  DMA_IRQHandler                       
00008711  Default_Handler                      
00008711  GROUP0_IRQHandler                    
00008711  HardFault_Handler                    
00008711  I2C0_IRQHandler                      
00008711  I2C1_IRQHandler                      
00008711  NMI_Handler                          
00008711  PendSV_Handler                       
00008711  RTC_IRQHandler                       
00008711  SPI0_IRQHandler                      
00008711  SPI1_IRQHandler                      
00008711  SVC_Handler                          
00008711  TIMA0_IRQHandler                     
00008711  TIMA1_IRQHandler                     
00008711  TIMG0_IRQHandler                     
00008711  TIMG12_IRQHandler                    
00008711  TIMG6_IRQHandler                     
00008711  TIMG7_IRQHandler                     
00008711  TIMG8_IRQHandler                     
00008711  UART0_IRQHandler                     
00008711  UART1_IRQHandler                     
00008711  UART2_IRQHandler                     
00008711  UART3_IRQHandler                     
00008714  C$$EXIT                              
00008715  HOSTexit                             
0000871d  Reset_Handler                        
00008731  _system_pre_init                     
00009340  __aeabi_ctype_table_                 
00009340  __aeabi_ctype_table_C                
000095a4  test                                 
00009713  reg                                  
000099a2  hw                                   
00009a10  __TI_Handler_Table_Base              
00009a1c  __TI_Handler_Table_Limit             
00009a24  __TI_CINIT_Base                      
00009a34  __TI_CINIT_Limit                     
00009a34  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  gMotorFrontBackup                    
202003ac  PID                                  
202003f4  Param                                
2020040c  quat                                 
2020041c  Flag                                 
20200427  more                                 
20200428  Data_Accel                           
2020042e  Data_Gyro                            
20200434  Data_Pitch                           
20200438  Data_Roll                            
2020043c  Data_Yaw                             
20200440  ExISR_Flag                           
20200444  sensor_timestamp                     
20200448  sensors                              
2020044a  stop_time_cnt                        
2020044c  time                                 
202004ab  Flag_MPU6050_Ready                   
202004ac  Data_MotorEncoder                    
202004b8  __aeabi_errno                        
202004bc  delayTick                            
202004c4  uwTick                               
202004cd  Task_Flag                            
202004d0  Task_State                           
202004d1  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[310 symbols]
