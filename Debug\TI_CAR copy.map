******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 23 14:48:04 2025

OUTPUT FILE NAME:   <TI_CAR copy.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000071a5


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00008c08  000173f8  R  X
  SRAM                  20200000   00008000  000006cd  00007933  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00008c08   00008c08    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00007cc0   00007cc0    r-x .text
  00007d80    00007d80    00000e40   00000e40    r-- .rodata
  00008bc0    00008bc0    00000048   00000048    r-- .cinit
20200000    20200000    000004cf   00000000    rw-
  20200000    20200000    0000044e   00000000    rw- .bss
  20200450    20200450    0000007f   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00007cc0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    000002e8     Task1.o (.text.Task_1)
                  000013d4    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  0000164c    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  00001884    0000022c     MPU6050.o (.text.Read_Quad)
                  00001ab0    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00001cdc    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001efc    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  000020f0    000001ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000022dc    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000024b8    000001b0     Task.o (.text.Task_Start)
                  00002668    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002808    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000299a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000299c    0000018c     Task_App.o (.text.Task_PID)
                  00002b28    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00002cb0    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002e28    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002f98    00000144     MPU6050.o (.text.MPU6050_Init)
                  000030dc    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00003218    00000134     PID_Param.o (.text.VelocityRing_PID_Realize)
                  0000334c    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00003480    00000134     libc.a : qsort.c.obj (.text.qsort)
                  000035b4    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  000036e4    00000128     inv_mpu.o (.text.mpu_init)
                  0000380c    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  00003930    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003a50    00000114     Interrupt.o (.text.GROUP1_IRQHandler)
                  00003b64    00000110     OLED.o (.text.OLED_Init)
                  00003c74    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003d80    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003e88    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00003f8c    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  0000408c    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00004178    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  0000425c    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  00004340    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  0000441c    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000044f4    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000045cc    000000d4     inv_mpu.o (.text.set_int_enable)
                  000046a0    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00004770    000000cc     Motor.o (.text.read_encoder)
                  0000483c    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  00004900    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  000049c4    000000bc     PID_Param.o (.text.LocationRing_PID_Realize)
                  00004a80    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00004b3c    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00004bf4    000000b4     Task.o (.text.Task_Add)
                  00004ca8    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00004d54    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00004e00    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00004ea2    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00004ea4    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00004f40    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00004fd8    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  00005070    00000096     MPU6050.o (.text.inv_row_2_scale)
                  00005106    00000002     --HOLE-- [fill = 0]
                  00005108    00000094     MyConfig.o (.text.Car_Tracking)
                  0000519c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  00005228    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000052b4    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00005340    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  000053cc    0000008c     main.o (.text.main)
                  00005458    00000084     Serial.o (.text.MyPrintf)
                  000054dc    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00005560    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000055e4    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00005666    00000080     Motor.o (.text.Load_Motor_PWM)
                  000056e6    00000002     --HOLE-- [fill = 0]
                  000056e8    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00005764    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000057d8    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000057e0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005854    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  000058c8    00000070     Motor.o (.text.Motor_SetPWM)
                  00005938    0000006c     MyConfig.o (.text.LocationRing_Out)
                  000059a4    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00005a0e    00000002     --HOLE-- [fill = 0]
                  00005a10    00000068     Task_App.o (.text.Task_Init)
                  00005a78    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00005ae0    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00005b46    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00005bac    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00005c10    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00005c74    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00005cd6    00000002     --HOLE-- [fill = 0]
                  00005cd8    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00005d3a    00000002     --HOLE-- [fill = 0]
                  00005d3c    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00005d9c    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00005dfc    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00005e5c    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00005ebc    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00005f1a    00000002     --HOLE-- [fill = 0]
                  00005f1c    0000005c     Motor.o (.text.Motor_SetDirc)
                  00005f78    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00005fd4    0000005c     Task_App.o (.text.Task_Encoder)
                  00006030    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  0000608c    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  000060e8    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00006144    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  0000619c    00000058     Serial.o (.text.Serial_Init)
                  000061f4    00000058     Task_App.o (.text.Task_IdleFunction)
                  0000624c    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000062a4    00000058            : _printfi.c.obj (.text._pconv_f)
                  000062fc    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00006352    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000063a4    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  000063f4    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00006444    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00006494    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  000064e0    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  0000652c    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00006576    00000002     --HOLE-- [fill = 0]
                  00006578    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000065c0    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00006608    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  00006650    00000048     OLED.o (.text.mspm0_i2c_disable)
                  00006698    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000066dc    00000044     PID_Param.o (.text.PID_Param_Init)
                  00006720    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  00006764    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  000067a8    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  000067ec    00000040     MyConfig.o (.text.VelocityRing_Out)
                  0000682c    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0000686c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000068ac    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000068ec    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  0000692c    0000003e     Task.o (.text.Task_CMP)
                  0000696a    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  000069a8    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000069e4    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006a20    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006a5c    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00006a98    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00006ad4    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00006b10    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00006b4c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00006b88    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00006bc2    00000002     --HOLE-- [fill = 0]
                  00006bc4    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00006bfe    00000002     --HOLE-- [fill = 0]
                  00006c00    00000038     Motor.o (.text.Motor_Start)
                  00006c38    00000038     Task_App.o (.text.Task_Serial)
                  00006c70    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00006ca8    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006cdc    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006d10    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006d44    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00006d78    00000034     Interrupt.o (.text.SysTick_Handler)
                  00006dac    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00006de0    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  00006e12    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00006e44    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00006e74    00000030     Interrupt.o (.text.Interrupt_Init)
                  00006ea4    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00006ed4    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00006f04    00000030            : vsnprintf.c.obj (.text._outs)
                  00006f34    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  00006f64    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00006f94    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00006fc0    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00006fec    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00007014    00000028     OLED.o (.text.DL_Common_updateReg)
                  0000703c    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00007064    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  0000708c    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  000070b4    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000070dc    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00007104    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  0000712c    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00007154    00000028     SysTick.o (.text.SysTick_Increasment)
                  0000717c    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  000071a4    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000071cc    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  000071f2    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00007218    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  0000723e    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00007264    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00007288    00000024     MyConfig.o (.text.LocationRing_VelocityRing_Control)
                  000072ac    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  000072d0    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000072f2    00000002     --HOLE-- [fill = 0]
                  000072f4    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00007314    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007334    00000020     driverlib.a : dl_uart.o (.text.DL_UART_transmitDataBlocking)
                  00007354    00000020     SysTick.o (.text.Delay)
                  00007374    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00007394    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  000073b2    00000002     --HOLE-- [fill = 0]
                  000073b4    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  000073d2    00000002     --HOLE-- [fill = 0]
                  000073d4    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  000073f0    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  0000740c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00007428    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00007444    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00007460    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  0000747c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00007498    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000074b4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  000074d0    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  000074ec    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  00007508    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00007524    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00007540    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  0000755c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00007578    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00007594    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000075b0    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000075cc    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000075e8    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00007604    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  0000761c    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00007634    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  0000764c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00007664    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  0000767c    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00007694    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  000076ac    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  000076c4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000076dc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  000076f4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  0000770c    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00007724    00000018     Motor.o (.text.DL_GPIO_setPins)
                  0000773c    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00007754    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  0000776c    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00007784    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  0000779c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000077b4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000077cc    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000077e4    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  000077fc    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00007814    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  0000782c    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00007844    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  0000785c    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00007874    00000018     OLED.o (.text.DL_I2C_reset)
                  0000788c    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000078a4    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000078bc    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  000078d4    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000078ec    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00007904    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  0000791c    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00007934    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  0000794c    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00007964    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  0000797c    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00007994    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  000079ac    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000079c4    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000079dc    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  000079f4    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00007a0c    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00007a22    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00007a38    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00007a4e    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00007a64    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00007a7a    00000016     SysTick.o (.text.SysGetTick)
                  00007a90    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00007aa4    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00007ab8    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00007acc    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00007ae0    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00007af4    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00007b08    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00007b1c    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00007b30    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00007b44    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00007b58    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00007b6c    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00007b80    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00007b94    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00007ba8    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00007bbc    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00007bce    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00007be0    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00007bf2    00000002     --HOLE-- [fill = 0]
                  00007bf4    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00007c04    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00007c14    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00007c24    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00007c34    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00007c44    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00007c52    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00007c60    0000000e     MPU6050.o (.text.tap_cb)
                  00007c6e    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00007c7c    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00007c88    0000000c     SysTick.o (.text.Sys_GetTick)
                  00007c94    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00007ca0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00007caa    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007cb4    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00007cc4    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007cce    00000002     --HOLE-- [fill = 0]
                  00007cd0    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00007ce0    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007cea    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007cf4    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007cfe    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00007d08    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00007d18    0000000a     MPU6050.o (.text.android_orient_cb)
                  00007d22    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007d2a    00000002     --HOLE-- [fill = 0]
                  00007d2c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00007d34    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007d3a    00000002     --HOLE-- [fill = 0]
                  00007d3c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00007d4c    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007d52    00000006            : exit.c.obj (.text:abort)
                  00007d58    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00007d5c    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00007d60    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00007d64    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00007d68    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00007d78    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00007d7c    00000004     --HOLE-- [fill = 0]

.cinit     0    00008bc0    00000048     
                  00008bc0    0000001d     (.cinit..data.load) [load image, compression = lzss]
                  00008bdd    00000003     --HOLE-- [fill = 0]
                  00008be0    0000000c     (__TI_handler_table)
                  00008bec    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00008bf4    00000010     (__TI_cinit_table)
                  00008c04    00000004     --HOLE-- [fill = 0]

.rodata    0    00007d80    00000e40     
                  00007d80    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00008976    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00008980    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00008a81    00000007     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00008a88    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00008ac8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00008af0    00000028     inv_mpu.o (.rodata.test)
                  00008b18    0000001e     inv_mpu.o (.rodata.reg)
                  00008b36    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00008b38    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00008b50    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00008b68    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00008b79    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00008b8a    0000000d     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00008b97    00000001     --HOLE-- [fill = 0]
                  00008b98    0000000c     inv_mpu.o (.rodata.hw)
                  00008ba4    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  00008bac    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00008bb4    00000004     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00008bb8    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00008bbb    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00008bbd    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00008bbf    00000001     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000044e     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000bc     (.common:gMotorFrontBackup)
                  202003ac    00000048     (.common:PID)
                  202003f4    00000018     (.common:Param)
                  2020040c    00000010     (.common:quat)
                  2020041c    0000000b     (.common:Flag)
                  20200427    00000001     (.common:more)
                  20200428    00000006     (.common:Data_Accel)
                  2020042e    00000006     (.common:Data_Gyro)
                  20200434    00000004     (.common:Data_Pitch)
                  20200438    00000004     (.common:Data_Roll)
                  2020043c    00000004     (.common:Data_Yaw)
                  20200440    00000004     (.common:ExISR_Flag)
                  20200444    00000004     (.common:sensor_timestamp)
                  20200448    00000002     (.common:sensors)
                  2020044a    00000002     (.common:stop_time_cnt)
                  2020044c    00000002     (.common:time)

.data      0    20200450    0000007f     UNINITIALIZED
                  20200450    0000002c     inv_mpu.o (.data.st)
                  2020047c    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  2020048c    0000000e     MPU6050.o (.data.hal)
                  2020049a    00000009     MPU6050.o (.data.gyro_orientation)
                  202004a3    00000008     Task1.o (.data.Task_1.Task_1_Step)
                  202004ab    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  202004ac    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004b0    00000004     Interrupt.o (.data.GROUP1_IRQHandler.left_last_time)
                  202004b4    00000004     Interrupt.o (.data.GROUP1_IRQHandler.right_last_time)
                  202004b8    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004bc    00000004     SysTick.o (.data.delayTick)
                  202004c0    00000004     Motor.o (.data.read_encoder.Data_MotorEncoder_Old)
                  202004c4    00000004     SysTick.o (.data.uwTick)
                  202004c8    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004ca    00000001     Task1.o (.data.Task_1.Task_1_Flag)
                  202004cb    00000001     Key_Led.o (.data.Task_Flag)
                  202004cc    00000001     Task.o (.data.Task_Num)
                  202004cd    00000001     Key_Led.o (.data.Task_State)
                  202004ce    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3134    115       188    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         140     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3282    307       188    
                                                               
    .\APP\Src\
       Task_App.o                     736     32        6      
       Interrupt.o                    522     0         14     
    +--+------------------------------+-------+---------+---------+
       Total:                         1258    32        20     
                                                               
    .\BSP\Src\
       MPU6050.o                      2464    0         70     
       OLED.o                         1308    0         0      
       Serial.o                       424     0         512    
       Task.o                         674     0         241    
       Task1.o                        744     0         9      
       Motor.o                        660     0         4      
       PID_Param.o                    564     0         72     
       MyConfig.o                     356     0         39     
       SysTick.o                      106     0         8      
       Key_Led.o                      0       0         2      
    +--+------------------------------+-------+---------+---------+
       Total:                         7300    0         957    
                                                               
    .\DMP\
       inv_mpu_dmp_motion_driver.o    3110    3062      16     
       inv_mpu.o                      4600    82        44     
    +--+------------------------------+-------+---------+---------+
       Total:                         7710    3144      60     
                                                               
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      122     0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1144    0         0      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       vsnprintf.c.obj                136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       memcmp.c.obj                   32      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8272    355       4      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2930    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       65        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   31900   3903      1741   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00008bf4 records: 2, size/record: 8, table size: 16
	.data: load addr=00008bc0, load size=0000001d bytes, run addr=20200450, run size=0000007f bytes, compression=lzss
	.bss: load addr=00008bec, load size=00000008 bytes, run addr=20200000, run size=0000044e bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00008be0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002809     00007cb4     00007cb2   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00004179     00007cd0     00007ccc   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007ce8          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007cfc          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007d28          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00007d50          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003c75     00007d08     00007d06   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00002813     00007d3c     00007d38   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007d62          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   000071a5     00007d68     00007d64   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00007d59  ADC0_IRQHandler                      
00007d59  ADC1_IRQHandler                      
00007d59  AES_IRQHandler                       
00007d5c  C$$EXIT                              
00007d59  CANFD0_IRQHandler                    
00005109  Car_Tracking                         
00007d59  DAC0_IRQHandler                      
00007ca1  DL_Common_delayCycles                
00006495  DL_DMA_initChannel                   
00005ebd  DL_I2C_fillControllerTXFIFO          
00006a5d  DL_I2C_flushControllerTXFIFO         
0000723f  DL_I2C_setClockConfig                
00004341  DL_SYSCTL_configSYSPLL               
00005bad  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006699  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003e89  DL_Timer_initFourCCPWMMode           
00007595  DL_Timer_setCaptCompUpdateMethod     
00007935  DL_Timer_setCaptureCompareOutCtl     
00007c05  DL_Timer_setCaptureCompareValue      
000075b1  DL_Timer_setClockConfig              
00006579  DL_UART_init                         
00007bbd  DL_UART_setClockConfig               
00007335  DL_UART_transmitDataBlocking         
00007d59  DMA_IRQHandler                       
20200428  Data_Accel                           
2020042e  Data_Gyro                            
202004ac  Data_MotorEncoder                    
20200434  Data_Pitch                           
20200438  Data_Roll                            
2020043c  Data_Yaw                             
00007d59  Default_Handler                      
00007355  Delay                                
20200440  ExISR_Flag                           
2020041c  Flag                                 
202004ab  Flag_MPU6050_Ready                   
00007d59  GROUP0_IRQHandler                    
00003a51  GROUP1_IRQHandler                    
00007d5d  HOSTexit                             
00007d59  HardFault_Handler                    
00007d59  I2C0_IRQHandler                      
00007d59  I2C1_IRQHandler                      
000059a5  I2C_OLED_Clear                       
00004f41  I2C_OLED_WR_Byte                     
00005d3d  I2C_OLED_i2c_sda_unlock              
00006e75  Interrupt_Init                       
00005667  Load_Motor_PWM                       
00005939  LocationRing_Out                     
000049c5  LocationRing_PID_Realize             
00007289  LocationRing_VelocityRing_Control    
00002f99  MPU6050_Init                         
00005f1d  Motor_SetDirc                        
000058c9  Motor_SetPWM                         
00006c01  Motor_Start                          
00005459  MyPrintf                             
00007d59  NMI_Handler                          
00003b65  OLED_Init                            
202003ac  PID                                  
000066dd  PID_Param_Init                       
202003f4  Param                                
00007d59  PendSV_Handler                       
00007d59  RTC_IRQHandler                       
00001885  Read_Quad                            
00007d65  Reset_Handler                        
00007d59  SPI0_IRQHandler                      
00007d59  SPI1_IRQHandler                      
00007d59  SVC_Handler                          
00006ea5  SYSCFG_DL_DMA_CH_RX_init             
000079dd  SYSCFG_DL_DMA_CH_TX_init             
00007c7d  SYSCFG_DL_DMA_init                   
000020f1  SYSCFG_DL_GPIO_init                  
00006145  SYSCFG_DL_I2C_MPU6050_init           
00005c11  SYSCFG_DL_I2C_OLED_init              
0000519d  SYSCFG_DL_MotorFront_init            
00005f79  SYSCFG_DL_SYSCTL_init                
00007c15  SYSCFG_DL_SYSTICK_init               
000054dd  SYSCFG_DL_UART0_init                 
00006d45  SYSCFG_DL_init                       
00005229  SYSCFG_DL_initPower                  
0000619d  Serial_Init                          
20200000  Serial_RxData                        
00007a7b  SysGetTick                           
00006d79  SysTick_Handler                      
00007155  SysTick_Increasment                  
00007c89  Sys_GetTick                          
00007d59  TIMA0_IRQHandler                     
00007d59  TIMA1_IRQHandler                     
00007d59  TIMG0_IRQHandler                     
00007d59  TIMG12_IRQHandler                    
00007d59  TIMG6_IRQHandler                     
00007d59  TIMG7_IRQHandler                     
00007d59  TIMG8_IRQHandler                     
00007bcf  TI_memcpy_small                      
00007c6f  TI_memset_small                      
000010ed  Task_1                               
00004bf5  Task_Add                             
00005fd5  Task_Encoder                         
202004cb  Task_Flag                            
000061f5  Task_IdleFunction                    
00005a11  Task_Init                            
0000299d  Task_PID                             
00006c39  Task_Serial                          
000024b9  Task_Start                           
202004cd  Task_State                           
00007d59  UART0_IRQHandler                     
00007d59  UART1_IRQHandler                     
00007d59  UART2_IRQHandler                     
00007d59  UART3_IRQHandler                     
000067ed  VelocityRing_Out                     
00003219  VelocityRing_PID_Realize             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00008bf4  __TI_CINIT_Base                      
00008c04  __TI_CINIT_Limit                     
00008c04  __TI_CINIT_Warm                      
00008be0  __TI_Handler_Table_Base              
00008bec  __TI_Handler_Table_Limit             
00006b4d  __TI_auto_init_nobinit_nopinit       
000056e9  __TI_decompress_lzss                 
00007be1  __TI_decompress_none                 
0000624d  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00007c35  __TI_zero_init                       
00002813  __adddf3                             
000044ff  __addsf3                             
00008980  __aeabi_ctype_table_                 
00008980  __aeabi_ctype_table_C                
000057e1  __aeabi_d2f                          
0000652d  __aeabi_d2iz                         
00002813  __aeabi_dadd                         
00005c75  __aeabi_dcmpeq                       
00005cb1  __aeabi_dcmpge                       
00005cc5  __aeabi_dcmpgt                       
00005c9d  __aeabi_dcmple                       
00005c89  __aeabi_dcmplt                       
00003c75  __aeabi_ddiv                         
00004179  __aeabi_dmul                         
00002809  __aeabi_dsub                         
202004b8  __aeabi_errno                        
000057d9  __aeabi_errno_addr                   
0000686d  __aeabi_f2d                          
00006c71  __aeabi_f2iz                         
000044ff  __aeabi_fadd                         
00005cd9  __aeabi_fcmpeq                       
00005d15  __aeabi_fcmpge                       
00005d29  __aeabi_fcmpgt                       
00005d01  __aeabi_fcmple                       
00005ced  __aeabi_fcmplt                       
000055e5  __aeabi_fdiv                         
000052b5  __aeabi_fmul                         
000044f5  __aeabi_fsub                         
00006fc1  __aeabi_i2d                          
00006ad5  __aeabi_i2f                          
000062fd  __aeabi_idiv                         
0000299b  __aeabi_idiv0                        
000062fd  __aeabi_idivmod                      
00004ea3  __aeabi_ldiv0                        
000073b5  __aeabi_llsl                         
000072ad  __aeabi_lmul                         
00007c95  __aeabi_memclr                       
00007c95  __aeabi_memclr4                      
00007c95  __aeabi_memclr8                      
00007d2d  __aeabi_memcpy                       
00007d2d  __aeabi_memcpy4                      
00007d2d  __aeabi_memcpy8                      
00007c45  __aeabi_memset                       
00007c45  __aeabi_memset4                      
00007c45  __aeabi_memset8                      
0000717d  __aeabi_ui2f                         
0000682d  __aeabi_uidiv                        
0000682d  __aeabi_uidivmod                     
00007b6d  __aeabi_uldivmod                     
000073b5  __ashldi3                            
ffffffff  __binit__                            
00005a79  __cmpdf2                             
00006b89  __cmpsf2                             
00003c75  __divdf3                             
000055e5  __divsf3                             
00005a79  __eqdf2                              
00006b89  __eqsf2                              
0000686d  __extendsfdf2                        
0000652d  __fixdfsi                            
00006c71  __fixsfsi                            
00006fc1  __floatsidf                          
00006ad5  __floatsisf                          
0000717d  __floatunsisf                        
00005765  __gedf2                              
00006b11  __gesf2                              
00005765  __gtdf2                              
00006b11  __gtsf2                              
00005a79  __ledf2                              
00006b89  __lesf2                              
00005a79  __ltdf2                              
00006b89  __ltsf2                              
UNDEFED   __mpu_init                           
00004179  __muldf3                             
000072ad  __muldi3                             
00006bc5  __muldsi3                            
000052b5  __mulsf3                             
00005a79  __nedf2                              
00006b89  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002809  __subdf3                             
000044f5  __subsf3                             
000057e1  __truncdfsf2                         
00004e01  __udivmoddi4                         
000071a5  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00007d79  _system_pre_init                     
00007d53  abort                                
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00002b29  atan2                                
00002b29  atan2l                               
00000df5  atanl                                
000068ad  atoi                                 
ffffffff  binit                                
202004bc  delayTick                            
000065c1  dmp_enable_6x_lp_quat                
000013d5  dmp_enable_feature                   
00005d9d  dmp_enable_gyro_cal                  
00006609  dmp_enable_lp_quat                   
000075e9  dmp_load_motion_driver_firmware      
00001efd  dmp_read_fifo                        
00007b81  dmp_register_android_orient_cb       
00007b95  dmp_register_tap_cb                  
00004fd9  dmp_set_fifo_rate                    
00002cb1  dmp_set_orientation                  
00006721  dmp_set_shake_reject_thresh          
00006de1  dmp_set_shake_reject_time            
00006e13  dmp_set_shake_reject_timeout         
00005b47  dmp_set_tap_axes                     
00006765  dmp_set_tap_count                    
0000164d  dmp_set_tap_thresh                   
00006f35  dmp_set_tap_time                     
00006f65  dmp_set_tap_time_multi               
202004ce  enable_group1_irq                    
00006031  frexp                                
00006031  frexpl                               
202002f0  gMotorFrontBackup                    
00008b98  hw                                   
00000000  interruptVectors                     
0000441d  ldexp                                
0000441d  ldexpl                               
000053cd  main                                 
000072d1  memccpy                              
00007375  memcmp                               
20200427  more                                 
00005dfd  mpu6050_i2c_sda_unlock               
00004a81  mpu_configure_fifo                   
00005855  mpu_get_accel_fsr                    
00005e5d  mpu_get_gyro_fsr                     
00006dad  mpu_get_sample_rate                  
000036e5  mpu_init                             
0000380d  mpu_load_firmware                    
00003f8d  mpu_lp_accel_mode                    
00003d81  mpu_read_fifo_stream                 
00004ca9  mpu_read_mem                         
00001ab1  mpu_reset_fifo                       
0000425d  mpu_set_accel_fsr                    
00002669  mpu_set_bypass                       
00004b3d  mpu_set_dmp_state                    
0000483d  mpu_set_gyro_fsr                     
00004ea5  mpu_set_int_latched                  
000046a1  mpu_set_lpf                          
0000408d  mpu_set_sample_rate                  
000035b5  mpu_set_sensors                      
00004d55  mpu_write_mem                        
0000334d  mspm0_i2c_read                       
00004901  mspm0_i2c_write                      
00003481  qsort                                
2020040c  quat                                 
00004771  read_encoder                         
00008b18  reg                                  
0000441d  scalbn                               
0000441d  scalbnl                              
20200444  sensor_timestamp                     
20200448  sensors                              
00002e29  sqrt                                 
00002e29  sqrtl                                
2020044a  stop_time_cnt                        
00008af0  test                                 
2020044c  time                                 
202004c4  uwTick                               
000068ed  vsnprintf                            
00007c25  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  Task_1                               
000013d5  dmp_enable_feature                   
0000164d  dmp_set_tap_thresh                   
00001885  Read_Quad                            
00001ab1  mpu_reset_fifo                       
00001efd  dmp_read_fifo                        
000020f1  SYSCFG_DL_GPIO_init                  
000024b9  Task_Start                           
00002669  mpu_set_bypass                       
00002809  __aeabi_dsub                         
00002809  __subdf3                             
00002813  __adddf3                             
00002813  __aeabi_dadd                         
0000299b  __aeabi_idiv0                        
0000299d  Task_PID                             
00002b29  atan2                                
00002b29  atan2l                               
00002cb1  dmp_set_orientation                  
00002e29  sqrt                                 
00002e29  sqrtl                                
00002f99  MPU6050_Init                         
00003219  VelocityRing_PID_Realize             
0000334d  mspm0_i2c_read                       
00003481  qsort                                
000035b5  mpu_set_sensors                      
000036e5  mpu_init                             
0000380d  mpu_load_firmware                    
00003a51  GROUP1_IRQHandler                    
00003b65  OLED_Init                            
00003c75  __aeabi_ddiv                         
00003c75  __divdf3                             
00003d81  mpu_read_fifo_stream                 
00003e89  DL_Timer_initFourCCPWMMode           
00003f8d  mpu_lp_accel_mode                    
0000408d  mpu_set_sample_rate                  
00004179  __aeabi_dmul                         
00004179  __muldf3                             
0000425d  mpu_set_accel_fsr                    
00004341  DL_SYSCTL_configSYSPLL               
0000441d  ldexp                                
0000441d  ldexpl                               
0000441d  scalbn                               
0000441d  scalbnl                              
000044f5  __aeabi_fsub                         
000044f5  __subsf3                             
000044ff  __addsf3                             
000044ff  __aeabi_fadd                         
000046a1  mpu_set_lpf                          
00004771  read_encoder                         
0000483d  mpu_set_gyro_fsr                     
00004901  mspm0_i2c_write                      
000049c5  LocationRing_PID_Realize             
00004a81  mpu_configure_fifo                   
00004b3d  mpu_set_dmp_state                    
00004bf5  Task_Add                             
00004ca9  mpu_read_mem                         
00004d55  mpu_write_mem                        
00004e01  __udivmoddi4                         
00004ea3  __aeabi_ldiv0                        
00004ea5  mpu_set_int_latched                  
00004f41  I2C_OLED_WR_Byte                     
00004fd9  dmp_set_fifo_rate                    
00005109  Car_Tracking                         
0000519d  SYSCFG_DL_MotorFront_init            
00005229  SYSCFG_DL_initPower                  
000052b5  __aeabi_fmul                         
000052b5  __mulsf3                             
000053cd  main                                 
00005459  MyPrintf                             
000054dd  SYSCFG_DL_UART0_init                 
000055e5  __aeabi_fdiv                         
000055e5  __divsf3                             
00005667  Load_Motor_PWM                       
000056e9  __TI_decompress_lzss                 
00005765  __gedf2                              
00005765  __gtdf2                              
000057d9  __aeabi_errno_addr                   
000057e1  __aeabi_d2f                          
000057e1  __truncdfsf2                         
00005855  mpu_get_accel_fsr                    
000058c9  Motor_SetPWM                         
00005939  LocationRing_Out                     
000059a5  I2C_OLED_Clear                       
00005a11  Task_Init                            
00005a79  __cmpdf2                             
00005a79  __eqdf2                              
00005a79  __ledf2                              
00005a79  __ltdf2                              
00005a79  __nedf2                              
00005b47  dmp_set_tap_axes                     
00005bad  DL_SYSCTL_setHFCLKSourceHFXTParams   
00005c11  SYSCFG_DL_I2C_OLED_init              
00005c75  __aeabi_dcmpeq                       
00005c89  __aeabi_dcmplt                       
00005c9d  __aeabi_dcmple                       
00005cb1  __aeabi_dcmpge                       
00005cc5  __aeabi_dcmpgt                       
00005cd9  __aeabi_fcmpeq                       
00005ced  __aeabi_fcmplt                       
00005d01  __aeabi_fcmple                       
00005d15  __aeabi_fcmpge                       
00005d29  __aeabi_fcmpgt                       
00005d3d  I2C_OLED_i2c_sda_unlock              
00005d9d  dmp_enable_gyro_cal                  
00005dfd  mpu6050_i2c_sda_unlock               
00005e5d  mpu_get_gyro_fsr                     
00005ebd  DL_I2C_fillControllerTXFIFO          
00005f1d  Motor_SetDirc                        
00005f79  SYSCFG_DL_SYSCTL_init                
00005fd5  Task_Encoder                         
00006031  frexp                                
00006031  frexpl                               
00006145  SYSCFG_DL_I2C_MPU6050_init           
0000619d  Serial_Init                          
000061f5  Task_IdleFunction                    
0000624d  __TI_ltoa                            
000062fd  __aeabi_idiv                         
000062fd  __aeabi_idivmod                      
00006495  DL_DMA_initChannel                   
0000652d  __aeabi_d2iz                         
0000652d  __fixdfsi                            
00006579  DL_UART_init                         
000065c1  dmp_enable_6x_lp_quat                
00006609  dmp_enable_lp_quat                   
00006699  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000066dd  PID_Param_Init                       
00006721  dmp_set_shake_reject_thresh          
00006765  dmp_set_tap_count                    
000067ed  VelocityRing_Out                     
0000682d  __aeabi_uidiv                        
0000682d  __aeabi_uidivmod                     
0000686d  __aeabi_f2d                          
0000686d  __extendsfdf2                        
000068ad  atoi                                 
000068ed  vsnprintf                            
00006a5d  DL_I2C_flushControllerTXFIFO         
00006ad5  __aeabi_i2f                          
00006ad5  __floatsisf                          
00006b11  __gesf2                              
00006b11  __gtsf2                              
00006b4d  __TI_auto_init_nobinit_nopinit       
00006b89  __cmpsf2                             
00006b89  __eqsf2                              
00006b89  __lesf2                              
00006b89  __ltsf2                              
00006b89  __nesf2                              
00006bc5  __muldsi3                            
00006c01  Motor_Start                          
00006c39  Task_Serial                          
00006c71  __aeabi_f2iz                         
00006c71  __fixsfsi                            
00006d45  SYSCFG_DL_init                       
00006d79  SysTick_Handler                      
00006dad  mpu_get_sample_rate                  
00006de1  dmp_set_shake_reject_time            
00006e13  dmp_set_shake_reject_timeout         
00006e75  Interrupt_Init                       
00006ea5  SYSCFG_DL_DMA_CH_RX_init             
00006f35  dmp_set_tap_time                     
00006f65  dmp_set_tap_time_multi               
00006fc1  __aeabi_i2d                          
00006fc1  __floatsidf                          
00007155  SysTick_Increasment                  
0000717d  __aeabi_ui2f                         
0000717d  __floatunsisf                        
000071a5  _c_int00_noargs                      
0000723f  DL_I2C_setClockConfig                
00007289  LocationRing_VelocityRing_Control    
000072ad  __aeabi_lmul                         
000072ad  __muldi3                             
000072d1  memccpy                              
00007335  DL_UART_transmitDataBlocking         
00007355  Delay                                
00007375  memcmp                               
000073b5  __aeabi_llsl                         
000073b5  __ashldi3                            
00007595  DL_Timer_setCaptCompUpdateMethod     
000075b1  DL_Timer_setClockConfig              
000075e9  dmp_load_motion_driver_firmware      
00007935  DL_Timer_setCaptureCompareOutCtl     
000079dd  SYSCFG_DL_DMA_CH_TX_init             
00007a7b  SysGetTick                           
00007b6d  __aeabi_uldivmod                     
00007b81  dmp_register_android_orient_cb       
00007b95  dmp_register_tap_cb                  
00007bbd  DL_UART_setClockConfig               
00007bcf  TI_memcpy_small                      
00007be1  __TI_decompress_none                 
00007c05  DL_Timer_setCaptureCompareValue      
00007c15  SYSCFG_DL_SYSTICK_init               
00007c25  wcslen                               
00007c35  __TI_zero_init                       
00007c45  __aeabi_memset                       
00007c45  __aeabi_memset4                      
00007c45  __aeabi_memset8                      
00007c6f  TI_memset_small                      
00007c7d  SYSCFG_DL_DMA_init                   
00007c89  Sys_GetTick                          
00007c95  __aeabi_memclr                       
00007c95  __aeabi_memclr4                      
00007c95  __aeabi_memclr8                      
00007ca1  DL_Common_delayCycles                
00007d2d  __aeabi_memcpy                       
00007d2d  __aeabi_memcpy4                      
00007d2d  __aeabi_memcpy8                      
00007d53  abort                                
00007d59  ADC0_IRQHandler                      
00007d59  ADC1_IRQHandler                      
00007d59  AES_IRQHandler                       
00007d59  CANFD0_IRQHandler                    
00007d59  DAC0_IRQHandler                      
00007d59  DMA_IRQHandler                       
00007d59  Default_Handler                      
00007d59  GROUP0_IRQHandler                    
00007d59  HardFault_Handler                    
00007d59  I2C0_IRQHandler                      
00007d59  I2C1_IRQHandler                      
00007d59  NMI_Handler                          
00007d59  PendSV_Handler                       
00007d59  RTC_IRQHandler                       
00007d59  SPI0_IRQHandler                      
00007d59  SPI1_IRQHandler                      
00007d59  SVC_Handler                          
00007d59  TIMA0_IRQHandler                     
00007d59  TIMA1_IRQHandler                     
00007d59  TIMG0_IRQHandler                     
00007d59  TIMG12_IRQHandler                    
00007d59  TIMG6_IRQHandler                     
00007d59  TIMG7_IRQHandler                     
00007d59  TIMG8_IRQHandler                     
00007d59  UART0_IRQHandler                     
00007d59  UART1_IRQHandler                     
00007d59  UART2_IRQHandler                     
00007d59  UART3_IRQHandler                     
00007d5c  C$$EXIT                              
00007d5d  HOSTexit                             
00007d65  Reset_Handler                        
00007d79  _system_pre_init                     
00008980  __aeabi_ctype_table_                 
00008980  __aeabi_ctype_table_C                
00008af0  test                                 
00008b18  reg                                  
00008b98  hw                                   
00008be0  __TI_Handler_Table_Base              
00008bec  __TI_Handler_Table_Limit             
00008bf4  __TI_CINIT_Base                      
00008c04  __TI_CINIT_Limit                     
00008c04  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  gMotorFrontBackup                    
202003ac  PID                                  
202003f4  Param                                
2020040c  quat                                 
2020041c  Flag                                 
20200427  more                                 
20200428  Data_Accel                           
2020042e  Data_Gyro                            
20200434  Data_Pitch                           
20200438  Data_Roll                            
2020043c  Data_Yaw                             
20200440  ExISR_Flag                           
20200444  sensor_timestamp                     
20200448  sensors                              
2020044a  stop_time_cnt                        
2020044c  time                                 
202004ab  Flag_MPU6050_Ready                   
202004ac  Data_MotorEncoder                    
202004b8  __aeabi_errno                        
202004bc  delayTick                            
202004c4  uwTick                               
202004cb  Task_Flag                            
202004cd  Task_State                           
202004ce  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[303 symbols]
