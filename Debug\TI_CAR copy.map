******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 23 14:53:07 2025

OUTPUT FILE NAME:   <TI_CAR copy.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 0000726d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00008d08  000172f8  R  X
  SRAM                  20200000   00008000  000006ce  00007932  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00008d08   00008d08    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00007d90   00007d90    r-x .text
  00007e50    00007e50    00000e70   00000e70    r-- .rodata
  00008cc0    00008cc0    00000048   00000048    r-- .cinit
20200000    20200000    000004d0   00000000    rw-
  20200000    20200000    0000044e   00000000    rw- .bss
  20200450    20200450    00000080   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00007d90     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    000002e8     Task1.o (.text.Task_1)
                  000013d4    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  0000164c    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  00001884    0000022c     MPU6050.o (.text.Read_Quad)
                  00001ab0    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00001cdc    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001efc    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  000020f0    000001ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000022dc    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000024b8    000001b0     Task.o (.text.Task_Start)
                  00002668    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002808    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000299a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000299c    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00002b24    00000180     Task_App.o (.text.Task_PID)
                  00002ca4    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002e1c    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002f8c    00000144     MPU6050.o (.text.MPU6050_Init)
                  000030d0    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  0000320c    00000134     PID_Param.o (.text.VelocityRing_PID_Realize)
                  00003340    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00003474    00000134     libc.a : qsort.c.obj (.text.qsort)
                  000035a8    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  000036d8    00000128     inv_mpu.o (.text.mpu_init)
                  00003800    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  00003924    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003a44    00000114     Interrupt.o (.text.GROUP1_IRQHandler)
                  00003b58    00000110     OLED.o (.text.OLED_Init)
                  00003c68    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003d74    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003e7c    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00003f80    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00004080    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  0000416c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00004250    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  00004334    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00004410    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000044e8    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000045c0    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004694    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00004764    000000cc     Motor.o (.text.read_encoder)
                  00004830    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  000048f4    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  000049b8    000000bc     PID_Param.o (.text.LocationRing_PID_Realize)
                  00004a74    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00004b30    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00004be8    000000b4     Task.o (.text.Task_Add)
                  00004c9c    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00004d48    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00004df4    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00004e96    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00004e98    000000a0     Task_App.o (.text.Task_Serial)
                  00004f38    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00004fd4    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  0000506c    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  00005104    00000096     MPU6050.o (.text.inv_row_2_scale)
                  0000519a    00000002     --HOLE-- [fill = 0]
                  0000519c    00000094     MyConfig.o (.text.Car_Tracking)
                  00005230    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  000052bc    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00005348    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000053d4    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00005460    0000008c     main.o (.text.main)
                  000054ec    00000084     Serial.o (.text.MyPrintf)
                  00005570    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  000055f4    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00005678    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000056fa    00000080     Motor.o (.text.Load_Motor_PWM)
                  0000577a    00000002     --HOLE-- [fill = 0]
                  0000577c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000057f8    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  0000586c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00005870    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000058e4    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00005958    00000070     Motor.o (.text.Motor_SetPWM)
                  000059c8    00000070     Task.o (.text.Task_GetMaxUsed)
                  00005a38    0000006c     MyConfig.o (.text.LocationRing_Out)
                  00005aa4    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00005b0e    00000002     --HOLE-- [fill = 0]
                  00005b10    00000068     Task_App.o (.text.Task_Init)
                  00005b78    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00005be0    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00005c46    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00005cac    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00005d10    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00005d74    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00005dd6    00000002     --HOLE-- [fill = 0]
                  00005dd8    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00005e3a    00000002     --HOLE-- [fill = 0]
                  00005e3c    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00005e9c    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00005efc    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00005f5c    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00005fbc    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000601a    00000002     --HOLE-- [fill = 0]
                  0000601c    0000005c     Motor.o (.text.Motor_SetDirc)
                  00006078    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000060d4    0000005c     Task_App.o (.text.Task_Encoder)
                  00006130    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  0000618c    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  000061e8    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00006244    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  0000629c    00000058     Serial.o (.text.Serial_Init)
                  000062f4    00000058     Task_App.o (.text.Task_IdleFunction)
                  0000634c    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000063a4    00000058            : _printfi.c.obj (.text._pconv_f)
                  000063fc    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00006452    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000064a4    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  000064f4    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00006544    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00006594    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  000065e0    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  0000662c    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00006676    00000002     --HOLE-- [fill = 0]
                  00006678    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000066c0    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00006708    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  00006750    00000048     OLED.o (.text.mspm0_i2c_disable)
                  00006798    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000067dc    00000044     PID_Param.o (.text.PID_Param_Init)
                  00006820    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  00006864    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  000068a8    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  000068ec    00000040     MyConfig.o (.text.VelocityRing_Out)
                  0000692c    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0000696c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000069ac    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000069ec    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00006a2c    0000003e     Task.o (.text.Task_CMP)
                  00006a6a    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  00006aa8    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006ae4    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006b20    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006b5c    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00006b98    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00006bd4    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00006c10    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00006c4c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00006c88    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00006cc2    00000002     --HOLE-- [fill = 0]
                  00006cc4    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00006cfe    00000002     --HOLE-- [fill = 0]
                  00006d00    00000038     Motor.o (.text.Motor_Start)
                  00006d38    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00006d70    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006da4    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006dd8    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006e0c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00006e40    00000034     Interrupt.o (.text.SysTick_Handler)
                  00006e74    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00006ea8    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  00006eda    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00006f0c    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00006f3c    00000030     Interrupt.o (.text.Interrupt_Init)
                  00006f6c    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00006f9c    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00006fcc    00000030            : vsnprintf.c.obj (.text._outs)
                  00006ffc    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  0000702c    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  0000705c    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00007088    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000070b4    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  000070dc    00000028     OLED.o (.text.DL_Common_updateReg)
                  00007104    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000712c    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00007154    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  0000717c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000071a4    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  000071cc    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  000071f4    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  0000721c    00000028     SysTick.o (.text.SysTick_Increasment)
                  00007244    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  0000726c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00007294    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  000072ba    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  000072e0    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00007306    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  0000732c    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00007350    00000024     MyConfig.o (.text.LocationRing_VelocityRing_Control)
                  00007374    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00007398    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000073ba    00000002     --HOLE-- [fill = 0]
                  000073bc    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000073dc    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000073fc    00000020     driverlib.a : dl_uart.o (.text.DL_UART_transmitDataBlocking)
                  0000741c    00000020     SysTick.o (.text.Delay)
                  0000743c    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  0000745c    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0000747a    00000002     --HOLE-- [fill = 0]
                  0000747c    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  0000749a    00000002     --HOLE-- [fill = 0]
                  0000749c    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  000074b8    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  000074d4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000074f0    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  0000750c    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00007528    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00007544    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00007560    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  0000757c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00007598    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  000075b4    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  000075d0    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  000075ec    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00007608    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00007624    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00007640    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  0000765c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00007678    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00007694    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000076b0    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  000076cc    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000076e4    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  000076fc    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00007714    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  0000772c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00007744    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  0000775c    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00007774    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  0000778c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000077a4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  000077bc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000077d4    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  000077ec    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00007804    00000018     OLED.o (.text.DL_GPIO_setPins)
                  0000781c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00007834    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  0000784c    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00007864    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  0000787c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00007894    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000078ac    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  000078c4    00000018     OLED.o (.text.DL_I2C_enablePower)
                  000078dc    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000078f4    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  0000790c    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00007924    00000018     MPU6050.o (.text.DL_I2C_reset)
                  0000793c    00000018     OLED.o (.text.DL_I2C_reset)
                  00007954    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  0000796c    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00007984    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  0000799c    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000079b4    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000079cc    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000079e4    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000079fc    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00007a14    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00007a2c    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00007a44    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00007a5c    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00007a74    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00007a8c    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00007aa4    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00007abc    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00007ad4    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00007aea    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00007b00    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00007b16    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00007b2c    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00007b42    00000016     SysTick.o (.text.SysGetTick)
                  00007b58    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00007b6c    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00007b80    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00007b94    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00007ba8    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00007bbc    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00007bd0    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00007be4    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00007bf8    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00007c0c    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00007c20    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00007c34    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00007c48    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00007c5c    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00007c70    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00007c84    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00007c96    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00007ca8    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00007cba    00000002     --HOLE-- [fill = 0]
                  00007cbc    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00007ccc    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00007cdc    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00007cec    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00007cfc    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00007d0c    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00007d1a    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00007d28    0000000e     MPU6050.o (.text.tap_cb)
                  00007d36    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00007d44    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00007d50    0000000c     SysTick.o (.text.Sys_GetTick)
                  00007d5c    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00007d68    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00007d72    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007d7c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00007d8c    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007d96    00000002     --HOLE-- [fill = 0]
                  00007d98    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00007da8    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007db2    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007dbc    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007dc6    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00007dd0    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00007de0    0000000a     MPU6050.o (.text.android_orient_cb)
                  00007dea    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007df2    00000002     --HOLE-- [fill = 0]
                  00007df4    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00007dfc    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00007e04    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007e0a    00000002     --HOLE-- [fill = 0]
                  00007e0c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00007e1c    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007e22    00000006            : exit.c.obj (.text:abort)
                  00007e28    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00007e2c    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00007e30    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00007e34    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00007e44    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00007e48    00000008     --HOLE-- [fill = 0]

.cinit     0    00008cc0    00000048     
                  00008cc0    0000001f     (.cinit..data.load) [load image, compression = lzss]
                  00008cdf    00000001     --HOLE-- [fill = 0]
                  00008ce0    0000000c     (__TI_handler_table)
                  00008cec    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00008cf4    00000010     (__TI_cinit_table)
                  00008d04    00000004     --HOLE-- [fill = 0]

.rodata    0    00007e50    00000e70     
                  00007e50    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00008a46    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00008a50    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00008b51    00000007     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00008b58    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00008b98    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00008bc0    00000028     inv_mpu.o (.rodata.test)
                  00008be8    0000001e     inv_mpu.o (.rodata.reg)
                  00008c06    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00008c08    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00008c20    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00008c38    00000017     Task_App.o (.rodata.str1.5883415095785080416.1)
                  00008c4f    00000013     Task_App.o (.rodata.str1.14074990341397557290.1)
                  00008c62    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00008c73    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00008c84    0000000d     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00008c91    00000001     --HOLE-- [fill = 0]
                  00008c92    0000000c     inv_mpu.o (.rodata.hw)
                  00008c9e    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00008ca0    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  00008ca8    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00008cb0    00000004     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00008cb4    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00008cb7    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00008cb9    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000044e     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000bc     (.common:gMotorFrontBackup)
                  202003ac    00000048     (.common:PID)
                  202003f4    00000018     (.common:Param)
                  2020040c    00000010     (.common:quat)
                  2020041c    0000000b     (.common:Flag)
                  20200427    00000001     (.common:more)
                  20200428    00000006     (.common:Data_Accel)
                  2020042e    00000006     (.common:Data_Gyro)
                  20200434    00000004     (.common:Data_Pitch)
                  20200438    00000004     (.common:Data_Roll)
                  2020043c    00000004     (.common:Data_Yaw)
                  20200440    00000004     (.common:ExISR_Flag)
                  20200444    00000004     (.common:sensor_timestamp)
                  20200448    00000002     (.common:sensors)
                  2020044a    00000002     (.common:stop_time_cnt)
                  2020044c    00000002     (.common:time)

.data      0    20200450    00000080     UNINITIALIZED
                  20200450    0000002c     inv_mpu.o (.data.st)
                  2020047c    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  2020048c    0000000e     MPU6050.o (.data.hal)
                  2020049a    00000009     MPU6050.o (.data.gyro_orientation)
                  202004a3    00000008     Task1.o (.data.Task_1.Task_1_Step)
                  202004ab    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  202004ac    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004b0    00000004     Interrupt.o (.data.GROUP1_IRQHandler.left_last_time)
                  202004b4    00000004     Interrupt.o (.data.GROUP1_IRQHandler.right_last_time)
                  202004b8    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004bc    00000004     SysTick.o (.data.delayTick)
                  202004c0    00000004     Motor.o (.data.read_encoder.Data_MotorEncoder_Old)
                  202004c4    00000004     SysTick.o (.data.uwTick)
                  202004c8    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004ca    00000001     Task1.o (.data.Task_1.Task_1_Flag)
                  202004cb    00000001     Key_Led.o (.data.Task_Flag)
                  202004cc    00000001     Task.o (.data.Task_Num)
                  202004cd    00000001     Task_App.o (.data.Task_Serial.monitor_counter)
                  202004ce    00000001     Key_Led.o (.data.Task_State)
                  202004cf    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3134    115       188    
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         140     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3282    307       188    
                                                               
    .\APP\Src\
       Task_App.o                     828     74        7      
       Interrupt.o                    522     0         14     
    +--+------------------------------+-------+---------+---------+
       Total:                         1350    74        21     
                                                               
    .\BSP\Src\
       MPU6050.o                      2464    0         70     
       OLED.o                         1308    0         0      
       Task.o                         786     0         241    
       Serial.o                       424     0         512    
       Task1.o                        744     0         9      
       Motor.o                        660     0         4      
       PID_Param.o                    564     0         72     
       MyConfig.o                     356     0         39     
       SysTick.o                      106     0         8      
       Key_Led.o                      0       0         2      
    +--+------------------------------+-------+---------+---------+
       Total:                         7412    0         957    
                                                               
    .\DMP\
       inv_mpu_dmp_motion_driver.o    3110    3062      16     
       inv_mpu.o                      4600    82        44     
    +--+------------------------------+-------+---------+---------+
       Total:                         7710    3144      60     
                                                               
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      122     0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1144    0         0      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       vsnprintf.c.obj                136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       memcmp.c.obj                   32      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8272    355       4      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\Ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2930    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       67        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   32104   3947      1742   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00008cf4 records: 2, size/record: 8, table size: 16
	.data: load addr=00008cc0, load size=0000001f bytes, run addr=20200450, run size=00000080 bytes, compression=lzss
	.bss: load addr=00008cec, load size=00000008 bytes, run addr=20200000, run size=0000044e bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00008ce0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002809     00007d7c     00007d7a   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   0000416d     00007d98     00007d94   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007db0          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007dc4          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007df0          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00007e20          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003c69     00007dd0     00007dce   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00002813     00007e0c     00007e08   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007e2e          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   0000726d     00007e34     00007e30   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000586d  ADC0_IRQHandler                      
0000586d  ADC1_IRQHandler                      
0000586d  AES_IRQHandler                       
00007e28  C$$EXIT                              
0000586d  CANFD0_IRQHandler                    
0000519d  Car_Tracking                         
0000586d  DAC0_IRQHandler                      
00007d69  DL_Common_delayCycles                
00006595  DL_DMA_initChannel                   
00005fbd  DL_I2C_fillControllerTXFIFO          
00006b5d  DL_I2C_flushControllerTXFIFO         
00007307  DL_I2C_setClockConfig                
00004335  DL_SYSCTL_configSYSPLL               
00005cad  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006799  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003e7d  DL_Timer_initFourCCPWMMode           
0000765d  DL_Timer_setCaptCompUpdateMethod     
000079fd  DL_Timer_setCaptureCompareOutCtl     
00007ccd  DL_Timer_setCaptureCompareValue      
00007679  DL_Timer_setClockConfig              
00006679  DL_UART_init                         
00007c85  DL_UART_setClockConfig               
000073fd  DL_UART_transmitDataBlocking         
0000586d  DMA_IRQHandler                       
20200428  Data_Accel                           
2020042e  Data_Gyro                            
202004ac  Data_MotorEncoder                    
20200434  Data_Pitch                           
20200438  Data_Roll                            
2020043c  Data_Yaw                             
0000586d  Default_Handler                      
0000741d  Delay                                
20200440  ExISR_Flag                           
2020041c  Flag                                 
202004ab  Flag_MPU6050_Ready                   
0000586d  GROUP0_IRQHandler                    
00003a45  GROUP1_IRQHandler                    
00007e29  HOSTexit                             
0000586d  HardFault_Handler                    
0000586d  I2C0_IRQHandler                      
0000586d  I2C1_IRQHandler                      
00005aa5  I2C_OLED_Clear                       
00004fd5  I2C_OLED_WR_Byte                     
00005e3d  I2C_OLED_i2c_sda_unlock              
00006f3d  Interrupt_Init                       
000056fb  Load_Motor_PWM                       
00005a39  LocationRing_Out                     
000049b9  LocationRing_PID_Realize             
00007351  LocationRing_VelocityRing_Control    
00002f8d  MPU6050_Init                         
0000601d  Motor_SetDirc                        
00005959  Motor_SetPWM                         
00006d01  Motor_Start                          
000054ed  MyPrintf                             
0000586d  NMI_Handler                          
00003b59  OLED_Init                            
202003ac  PID                                  
000067dd  PID_Param_Init                       
202003f4  Param                                
0000586d  PendSV_Handler                       
0000586d  RTC_IRQHandler                       
00001885  Read_Quad                            
00007e31  Reset_Handler                        
0000586d  SPI0_IRQHandler                      
0000586d  SPI1_IRQHandler                      
0000586d  SVC_Handler                          
00006f6d  SYSCFG_DL_DMA_CH_RX_init             
00007aa5  SYSCFG_DL_DMA_CH_TX_init             
00007d45  SYSCFG_DL_DMA_init                   
000020f1  SYSCFG_DL_GPIO_init                  
00006245  SYSCFG_DL_I2C_MPU6050_init           
00005d11  SYSCFG_DL_I2C_OLED_init              
00005231  SYSCFG_DL_MotorFront_init            
00006079  SYSCFG_DL_SYSCTL_init                
00007cdd  SYSCFG_DL_SYSTICK_init               
00005571  SYSCFG_DL_UART0_init                 
00006e0d  SYSCFG_DL_init                       
000052bd  SYSCFG_DL_initPower                  
0000629d  Serial_Init                          
20200000  Serial_RxData                        
00007b43  SysGetTick                           
00006e41  SysTick_Handler                      
0000721d  SysTick_Increasment                  
00007d51  Sys_GetTick                          
0000586d  TIMA0_IRQHandler                     
0000586d  TIMA1_IRQHandler                     
0000586d  TIMG0_IRQHandler                     
0000586d  TIMG12_IRQHandler                    
0000586d  TIMG6_IRQHandler                     
0000586d  TIMG7_IRQHandler                     
0000586d  TIMG8_IRQHandler                     
00007c97  TI_memcpy_small                      
00007d37  TI_memset_small                      
000010ed  Task_1                               
00004be9  Task_Add                             
000060d5  Task_Encoder                         
202004cb  Task_Flag                            
000059c9  Task_GetMaxUsed                      
000062f5  Task_IdleFunction                    
00005b11  Task_Init                            
00002b25  Task_PID                             
00004e99  Task_Serial                          
000024b9  Task_Start                           
202004ce  Task_State                           
0000586d  UART0_IRQHandler                     
0000586d  UART1_IRQHandler                     
0000586d  UART2_IRQHandler                     
0000586d  UART3_IRQHandler                     
UNDEFED   VOFA_ProcessCommand                  
000068ed  VelocityRing_Out                     
0000320d  VelocityRing_PID_Realize             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00008cf4  __TI_CINIT_Base                      
00008d04  __TI_CINIT_Limit                     
00008d04  __TI_CINIT_Warm                      
00008ce0  __TI_Handler_Table_Base              
00008cec  __TI_Handler_Table_Limit             
00006c4d  __TI_auto_init_nobinit_nopinit       
0000577d  __TI_decompress_lzss                 
00007ca9  __TI_decompress_none                 
0000634d  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00007cfd  __TI_zero_init                       
00002813  __adddf3                             
000044f3  __addsf3                             
00008a50  __aeabi_ctype_table_                 
00008a50  __aeabi_ctype_table_C                
00005871  __aeabi_d2f                          
0000662d  __aeabi_d2iz                         
00002813  __aeabi_dadd                         
00005d75  __aeabi_dcmpeq                       
00005db1  __aeabi_dcmpge                       
00005dc5  __aeabi_dcmpgt                       
00005d9d  __aeabi_dcmple                       
00005d89  __aeabi_dcmplt                       
00003c69  __aeabi_ddiv                         
0000416d  __aeabi_dmul                         
00002809  __aeabi_dsub                         
202004b8  __aeabi_errno                        
00007df5  __aeabi_errno_addr                   
0000696d  __aeabi_f2d                          
00006d39  __aeabi_f2iz                         
000044f3  __aeabi_fadd                         
00005dd9  __aeabi_fcmpeq                       
00005e15  __aeabi_fcmpge                       
00005e29  __aeabi_fcmpgt                       
00005e01  __aeabi_fcmple                       
00005ded  __aeabi_fcmplt                       
00005679  __aeabi_fdiv                         
00005349  __aeabi_fmul                         
000044e9  __aeabi_fsub                         
00007089  __aeabi_i2d                          
00006bd5  __aeabi_i2f                          
000063fd  __aeabi_idiv                         
0000299b  __aeabi_idiv0                        
000063fd  __aeabi_idivmod                      
00004e97  __aeabi_ldiv0                        
0000747d  __aeabi_llsl                         
00007375  __aeabi_lmul                         
00007d5d  __aeabi_memclr                       
00007d5d  __aeabi_memclr4                      
00007d5d  __aeabi_memclr8                      
00007dfd  __aeabi_memcpy                       
00007dfd  __aeabi_memcpy4                      
00007dfd  __aeabi_memcpy8                      
00007d0d  __aeabi_memset                       
00007d0d  __aeabi_memset4                      
00007d0d  __aeabi_memset8                      
00007245  __aeabi_ui2f                         
0000692d  __aeabi_uidiv                        
0000692d  __aeabi_uidivmod                     
00007c35  __aeabi_uldivmod                     
0000747d  __ashldi3                            
ffffffff  __binit__                            
00005b79  __cmpdf2                             
00006c89  __cmpsf2                             
00003c69  __divdf3                             
00005679  __divsf3                             
00005b79  __eqdf2                              
00006c89  __eqsf2                              
0000696d  __extendsfdf2                        
0000662d  __fixdfsi                            
00006d39  __fixsfsi                            
00007089  __floatsidf                          
00006bd5  __floatsisf                          
00007245  __floatunsisf                        
000057f9  __gedf2                              
00006c11  __gesf2                              
000057f9  __gtdf2                              
00006c11  __gtsf2                              
00005b79  __ledf2                              
00006c89  __lesf2                              
00005b79  __ltdf2                              
00006c89  __ltsf2                              
UNDEFED   __mpu_init                           
0000416d  __muldf3                             
00007375  __muldi3                             
00006cc5  __muldsi3                            
00005349  __mulsf3                             
00005b79  __nedf2                              
00006c89  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002809  __subdf3                             
000044e9  __subsf3                             
00005871  __truncdfsf2                         
00004df5  __udivmoddi4                         
0000726d  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00007e45  _system_pre_init                     
00007e23  abort                                
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
0000299d  atan2                                
0000299d  atan2l                               
00000df5  atanl                                
000069ad  atoi                                 
ffffffff  binit                                
202004bc  delayTick                            
000066c1  dmp_enable_6x_lp_quat                
000013d5  dmp_enable_feature                   
00005e9d  dmp_enable_gyro_cal                  
00006709  dmp_enable_lp_quat                   
000076b1  dmp_load_motion_driver_firmware      
00001efd  dmp_read_fifo                        
00007c49  dmp_register_android_orient_cb       
00007c5d  dmp_register_tap_cb                  
0000506d  dmp_set_fifo_rate                    
00002ca5  dmp_set_orientation                  
00006821  dmp_set_shake_reject_thresh          
00006ea9  dmp_set_shake_reject_time            
00006edb  dmp_set_shake_reject_timeout         
00005c47  dmp_set_tap_axes                     
00006865  dmp_set_tap_count                    
0000164d  dmp_set_tap_thresh                   
00006ffd  dmp_set_tap_time                     
0000702d  dmp_set_tap_time_multi               
202004cf  enable_group1_irq                    
00006131  frexp                                
00006131  frexpl                               
202002f0  gMotorFrontBackup                    
00008c92  hw                                   
00000000  interruptVectors                     
00004411  ldexp                                
00004411  ldexpl                               
00005461  main                                 
00007399  memccpy                              
0000743d  memcmp                               
20200427  more                                 
00005efd  mpu6050_i2c_sda_unlock               
00004a75  mpu_configure_fifo                   
000058e5  mpu_get_accel_fsr                    
00005f5d  mpu_get_gyro_fsr                     
00006e75  mpu_get_sample_rate                  
000036d9  mpu_init                             
00003801  mpu_load_firmware                    
00003f81  mpu_lp_accel_mode                    
00003d75  mpu_read_fifo_stream                 
00004c9d  mpu_read_mem                         
00001ab1  mpu_reset_fifo                       
00004251  mpu_set_accel_fsr                    
00002669  mpu_set_bypass                       
00004b31  mpu_set_dmp_state                    
00004831  mpu_set_gyro_fsr                     
00004f39  mpu_set_int_latched                  
00004695  mpu_set_lpf                          
00004081  mpu_set_sample_rate                  
000035a9  mpu_set_sensors                      
00004d49  mpu_write_mem                        
00003341  mspm0_i2c_read                       
000048f5  mspm0_i2c_write                      
00003475  qsort                                
2020040c  quat                                 
00004765  read_encoder                         
00008be8  reg                                  
00004411  scalbn                               
00004411  scalbnl                              
20200444  sensor_timestamp                     
20200448  sensors                              
00002e1d  sqrt                                 
00002e1d  sqrtl                                
2020044a  stop_time_cnt                        
00008bc0  test                                 
2020044c  time                                 
202004c4  uwTick                               
000069ed  vsnprintf                            
00007ced  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  Task_1                               
000013d5  dmp_enable_feature                   
0000164d  dmp_set_tap_thresh                   
00001885  Read_Quad                            
00001ab1  mpu_reset_fifo                       
00001efd  dmp_read_fifo                        
000020f1  SYSCFG_DL_GPIO_init                  
000024b9  Task_Start                           
00002669  mpu_set_bypass                       
00002809  __aeabi_dsub                         
00002809  __subdf3                             
00002813  __adddf3                             
00002813  __aeabi_dadd                         
0000299b  __aeabi_idiv0                        
0000299d  atan2                                
0000299d  atan2l                               
00002b25  Task_PID                             
00002ca5  dmp_set_orientation                  
00002e1d  sqrt                                 
00002e1d  sqrtl                                
00002f8d  MPU6050_Init                         
0000320d  VelocityRing_PID_Realize             
00003341  mspm0_i2c_read                       
00003475  qsort                                
000035a9  mpu_set_sensors                      
000036d9  mpu_init                             
00003801  mpu_load_firmware                    
00003a45  GROUP1_IRQHandler                    
00003b59  OLED_Init                            
00003c69  __aeabi_ddiv                         
00003c69  __divdf3                             
00003d75  mpu_read_fifo_stream                 
00003e7d  DL_Timer_initFourCCPWMMode           
00003f81  mpu_lp_accel_mode                    
00004081  mpu_set_sample_rate                  
0000416d  __aeabi_dmul                         
0000416d  __muldf3                             
00004251  mpu_set_accel_fsr                    
00004335  DL_SYSCTL_configSYSPLL               
00004411  ldexp                                
00004411  ldexpl                               
00004411  scalbn                               
00004411  scalbnl                              
000044e9  __aeabi_fsub                         
000044e9  __subsf3                             
000044f3  __addsf3                             
000044f3  __aeabi_fadd                         
00004695  mpu_set_lpf                          
00004765  read_encoder                         
00004831  mpu_set_gyro_fsr                     
000048f5  mspm0_i2c_write                      
000049b9  LocationRing_PID_Realize             
00004a75  mpu_configure_fifo                   
00004b31  mpu_set_dmp_state                    
00004be9  Task_Add                             
00004c9d  mpu_read_mem                         
00004d49  mpu_write_mem                        
00004df5  __udivmoddi4                         
00004e97  __aeabi_ldiv0                        
00004e99  Task_Serial                          
00004f39  mpu_set_int_latched                  
00004fd5  I2C_OLED_WR_Byte                     
0000506d  dmp_set_fifo_rate                    
0000519d  Car_Tracking                         
00005231  SYSCFG_DL_MotorFront_init            
000052bd  SYSCFG_DL_initPower                  
00005349  __aeabi_fmul                         
00005349  __mulsf3                             
00005461  main                                 
000054ed  MyPrintf                             
00005571  SYSCFG_DL_UART0_init                 
00005679  __aeabi_fdiv                         
00005679  __divsf3                             
000056fb  Load_Motor_PWM                       
0000577d  __TI_decompress_lzss                 
000057f9  __gedf2                              
000057f9  __gtdf2                              
0000586d  ADC0_IRQHandler                      
0000586d  ADC1_IRQHandler                      
0000586d  AES_IRQHandler                       
0000586d  CANFD0_IRQHandler                    
0000586d  DAC0_IRQHandler                      
0000586d  DMA_IRQHandler                       
0000586d  Default_Handler                      
0000586d  GROUP0_IRQHandler                    
0000586d  HardFault_Handler                    
0000586d  I2C0_IRQHandler                      
0000586d  I2C1_IRQHandler                      
0000586d  NMI_Handler                          
0000586d  PendSV_Handler                       
0000586d  RTC_IRQHandler                       
0000586d  SPI0_IRQHandler                      
0000586d  SPI1_IRQHandler                      
0000586d  SVC_Handler                          
0000586d  TIMA0_IRQHandler                     
0000586d  TIMA1_IRQHandler                     
0000586d  TIMG0_IRQHandler                     
0000586d  TIMG12_IRQHandler                    
0000586d  TIMG6_IRQHandler                     
0000586d  TIMG7_IRQHandler                     
0000586d  TIMG8_IRQHandler                     
0000586d  UART0_IRQHandler                     
0000586d  UART1_IRQHandler                     
0000586d  UART2_IRQHandler                     
0000586d  UART3_IRQHandler                     
00005871  __aeabi_d2f                          
00005871  __truncdfsf2                         
000058e5  mpu_get_accel_fsr                    
00005959  Motor_SetPWM                         
000059c9  Task_GetMaxUsed                      
00005a39  LocationRing_Out                     
00005aa5  I2C_OLED_Clear                       
00005b11  Task_Init                            
00005b79  __cmpdf2                             
00005b79  __eqdf2                              
00005b79  __ledf2                              
00005b79  __ltdf2                              
00005b79  __nedf2                              
00005c47  dmp_set_tap_axes                     
00005cad  DL_SYSCTL_setHFCLKSourceHFXTParams   
00005d11  SYSCFG_DL_I2C_OLED_init              
00005d75  __aeabi_dcmpeq                       
00005d89  __aeabi_dcmplt                       
00005d9d  __aeabi_dcmple                       
00005db1  __aeabi_dcmpge                       
00005dc5  __aeabi_dcmpgt                       
00005dd9  __aeabi_fcmpeq                       
00005ded  __aeabi_fcmplt                       
00005e01  __aeabi_fcmple                       
00005e15  __aeabi_fcmpge                       
00005e29  __aeabi_fcmpgt                       
00005e3d  I2C_OLED_i2c_sda_unlock              
00005e9d  dmp_enable_gyro_cal                  
00005efd  mpu6050_i2c_sda_unlock               
00005f5d  mpu_get_gyro_fsr                     
00005fbd  DL_I2C_fillControllerTXFIFO          
0000601d  Motor_SetDirc                        
00006079  SYSCFG_DL_SYSCTL_init                
000060d5  Task_Encoder                         
00006131  frexp                                
00006131  frexpl                               
00006245  SYSCFG_DL_I2C_MPU6050_init           
0000629d  Serial_Init                          
000062f5  Task_IdleFunction                    
0000634d  __TI_ltoa                            
000063fd  __aeabi_idiv                         
000063fd  __aeabi_idivmod                      
00006595  DL_DMA_initChannel                   
0000662d  __aeabi_d2iz                         
0000662d  __fixdfsi                            
00006679  DL_UART_init                         
000066c1  dmp_enable_6x_lp_quat                
00006709  dmp_enable_lp_quat                   
00006799  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000067dd  PID_Param_Init                       
00006821  dmp_set_shake_reject_thresh          
00006865  dmp_set_tap_count                    
000068ed  VelocityRing_Out                     
0000692d  __aeabi_uidiv                        
0000692d  __aeabi_uidivmod                     
0000696d  __aeabi_f2d                          
0000696d  __extendsfdf2                        
000069ad  atoi                                 
000069ed  vsnprintf                            
00006b5d  DL_I2C_flushControllerTXFIFO         
00006bd5  __aeabi_i2f                          
00006bd5  __floatsisf                          
00006c11  __gesf2                              
00006c11  __gtsf2                              
00006c4d  __TI_auto_init_nobinit_nopinit       
00006c89  __cmpsf2                             
00006c89  __eqsf2                              
00006c89  __lesf2                              
00006c89  __ltsf2                              
00006c89  __nesf2                              
00006cc5  __muldsi3                            
00006d01  Motor_Start                          
00006d39  __aeabi_f2iz                         
00006d39  __fixsfsi                            
00006e0d  SYSCFG_DL_init                       
00006e41  SysTick_Handler                      
00006e75  mpu_get_sample_rate                  
00006ea9  dmp_set_shake_reject_time            
00006edb  dmp_set_shake_reject_timeout         
00006f3d  Interrupt_Init                       
00006f6d  SYSCFG_DL_DMA_CH_RX_init             
00006ffd  dmp_set_tap_time                     
0000702d  dmp_set_tap_time_multi               
00007089  __aeabi_i2d                          
00007089  __floatsidf                          
0000721d  SysTick_Increasment                  
00007245  __aeabi_ui2f                         
00007245  __floatunsisf                        
0000726d  _c_int00_noargs                      
00007307  DL_I2C_setClockConfig                
00007351  LocationRing_VelocityRing_Control    
00007375  __aeabi_lmul                         
00007375  __muldi3                             
00007399  memccpy                              
000073fd  DL_UART_transmitDataBlocking         
0000741d  Delay                                
0000743d  memcmp                               
0000747d  __aeabi_llsl                         
0000747d  __ashldi3                            
0000765d  DL_Timer_setCaptCompUpdateMethod     
00007679  DL_Timer_setClockConfig              
000076b1  dmp_load_motion_driver_firmware      
000079fd  DL_Timer_setCaptureCompareOutCtl     
00007aa5  SYSCFG_DL_DMA_CH_TX_init             
00007b43  SysGetTick                           
00007c35  __aeabi_uldivmod                     
00007c49  dmp_register_android_orient_cb       
00007c5d  dmp_register_tap_cb                  
00007c85  DL_UART_setClockConfig               
00007c97  TI_memcpy_small                      
00007ca9  __TI_decompress_none                 
00007ccd  DL_Timer_setCaptureCompareValue      
00007cdd  SYSCFG_DL_SYSTICK_init               
00007ced  wcslen                               
00007cfd  __TI_zero_init                       
00007d0d  __aeabi_memset                       
00007d0d  __aeabi_memset4                      
00007d0d  __aeabi_memset8                      
00007d37  TI_memset_small                      
00007d45  SYSCFG_DL_DMA_init                   
00007d51  Sys_GetTick                          
00007d5d  __aeabi_memclr                       
00007d5d  __aeabi_memclr4                      
00007d5d  __aeabi_memclr8                      
00007d69  DL_Common_delayCycles                
00007df5  __aeabi_errno_addr                   
00007dfd  __aeabi_memcpy                       
00007dfd  __aeabi_memcpy4                      
00007dfd  __aeabi_memcpy8                      
00007e23  abort                                
00007e28  C$$EXIT                              
00007e29  HOSTexit                             
00007e31  Reset_Handler                        
00007e45  _system_pre_init                     
00008a50  __aeabi_ctype_table_                 
00008a50  __aeabi_ctype_table_C                
00008bc0  test                                 
00008be8  reg                                  
00008c92  hw                                   
00008ce0  __TI_Handler_Table_Base              
00008cec  __TI_Handler_Table_Limit             
00008cf4  __TI_CINIT_Base                      
00008d04  __TI_CINIT_Limit                     
00008d04  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  gMotorFrontBackup                    
202003ac  PID                                  
202003f4  Param                                
2020040c  quat                                 
2020041c  Flag                                 
20200427  more                                 
20200428  Data_Accel                           
2020042e  Data_Gyro                            
20200434  Data_Pitch                           
20200438  Data_Roll                            
2020043c  Data_Yaw                             
20200440  ExISR_Flag                           
20200444  sensor_timestamp                     
20200448  sensors                              
2020044a  stop_time_cnt                        
2020044c  time                                 
202004ab  Flag_MPU6050_Ready                   
202004ac  Data_MotorEncoder                    
202004b8  __aeabi_errno                        
202004bc  delayTick                            
202004c4  uwTick                               
202004cb  Task_Flag                            
202004ce  Task_State                           
202004cf  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   VOFA_ProcessCommand                  
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[305 symbols]
