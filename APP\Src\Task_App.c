/**
 * @file Task_App.c
 * <AUTHOR> name void Task_OLED(void *para)
 * @brief 任务实现层
 * @version 0.1
 * @date 2025-07-12
 *
 * @copyright Copyright (c) 2025
 *
 */
#include "Task_App.h"

/*Flag*/
bool Flag_LED = false;
extern bool Flag_MPU6050_Ready; //MPU6050是否准备好

/*Data*/
PID_Def_t Data_MotorPID[2]; //电机PID对象 左前 右前
uint8_t Data_MotorPWM_Duty[2] = {0, 0}; //电机PWM值 左前 右前
int16_t Data_MotorEncoder[2] = {0}; //四个电机的编码值 左前 右前
extern uint16_t Data_Gyro[3]; // 陀螺仪原始数据[X轴, Y轴, Z轴] - 角速度传感器输出值
extern uint16_t Data_Accel[3]; // 加速度计原始数据[X轴, Y轴, Z轴] - 线性加速度传感器输出值
extern float Data_Pitch; // 俯仰角(度) - 绕X轴旋转角度，前后倾斜
extern float Data_Roll; // 横滚角(度) - 绕Y轴旋转角度，左右倾斜
extern float Data_Yaw; // 偏航角(度) - 绕Z轴旋转角度，水平转向

void Task_Key(void *para);
void Task_LED(void *para);
void Task_Motor(void *para);
void Task_Serial(void *para);
void Task_OLED(void *para);
void Task_Encoder(void *para);
void Task_PID(void *para);

void Task_Init(void)
{
    Motor_Start(); //开启电机
    Serial_Init(); //初始化串口
    OLED_Init(); //OLED初始化
    MPU6050_Init(); //MPU6050初始化

    // for (uint8_t i = 0; i < 2; i++)
    // {
    //     PID_Init(&Data_MotorPID[i]);
    // }

    PID_Param_Init(); //串级PID初始化

    Interrupt_Init(); //中断初始化

    // Task_Add("Motor", Task_Motor, 10, (void *)Data_MotorPWM_Duty, 0);
    Task_Add("PID", Task_PID, 5, NULL, 0);
    Task_Add("Encoder", Task_Encoder, 10, NULL, 1);
    // Task_Add("Key", Task_Key, 10, NULL, 1);
    // Task_Add("LED", Task_LED, 100, NULL, 2);
    Task_Add("Serial", Task_Serial, 200, NULL, 2);
    // Task_Add("OLED", Task_OLED, 50, NULL, 2);
}

//空闲任务函数
void Task_IdleFunction(void)
{
    if (Flag_MPU6050_Ready == true) //检测到标志位后读取
    {
        Flag_MPU6050_Ready = false;
        Read_Quad();
    }
    if (!enable_group1_irq)
    {
        static uint16_t CNT = 0;
        if (++CNT == 5000)
        {
            CNT = 0;
            Read_Quad();
        }
    }
}

void Task_OLED(void *para)
{
    /* 陀螺仪角度显示 */
    OLED_Printf(0, 16 * 0, 16, "Pitch:%.2f ", Data_Pitch);
    OLED_Printf(0, 16 * 1, 16, "Roll:%.2f ", Data_Roll);
    OLED_Printf(0, 16 * 2, 16, "Yaw:%.2f ", Data_Yaw);
    OLED_Printf(0, 16 * 3, 16, "SysTick:%u ", (uint16_t)(uwTick / 1000));

    /* 电机编码器显示函数 */
    // OLED_Printf(0, 16 * 0, 16, "[1]:%4d", Data_MotorEncoder[0]);
    // OLED_Printf(0, 16 * 1, 16, "[2]:%4d", Data_MotorEncoder[1]);

}

void Task_Key(void *para)
{
    static uint8_t Key_Old = 0;
    uint8_t Key_Temp = Key_Read();
    uint8_t Key_Val = (Key_Temp ^ Key_Old) & Key_Temp;
    Key_Old = Key_Temp;

    switch(Key_Val)
	{
		case 5://开始执行总任务
			Task_Flag ^=1;
		break;

		case 1://选择任务1
			Task_State = 1;
		break;
	}


    // if (Key_Val)
    // {
    //     Flag_LED ^= 1;
    //     for (uint8_t i = 0; i < 2; i++)
    //     {
    //         Data_MotorPWM_Duty[i] += 10;
    //         if (Data_MotorPWM_Duty[i] > 100) Data_MotorPWM_Duty[i] = 10;
    //     }
    // }
}

void Task_LED(void *para)
{
    // if (Flag_LED == false)
    // {
    //     LED_BOARD_OFF();
    // }
    // else
    // {
    //     LED_BOARD_ON();
    // }
}

void Task_Motor(void *para)
{
    uint8_t *TempVal = (uint8_t *)para;

    //PID
    for (uint8_t i = 0; i < 2; i++)
    {
        Data_MotorPID[i].Acutal_Now = TempVal[i];
        PID_Prosc(&Data_MotorPID[i]);
    }

    //PWM Set
    Motor_SetPWM(MOTOR_FONT_LEFT, Data_MotorPID[0].Out);
    Motor_SetPWM(MOTOR_FONT_RIGHT, Data_MotorPID[1].Out);
    // Motor_SetPWM(MOTOR_BACK_LEFT, Data_MotorPID[2].Out);
    // Motor_SetPWM(MOTOR_BACK_RIGHT, Data_MotorPID[3].Out);
}

//vofa
void Task_Serial(void *para)
{
    /* VOFA调试命令处理 - 实时调整PID参数 */
    VOFA_ProcessCommand();

    /* 编码器数据发送函数 */
    // MyPrintf("%d,%d,%.2f,%d,%d\r\n",Param.UnitTime_Motor1Pluse,Param.UnitTime_Motor2Pluse,Param.Distance_Motor1Curret,Param.Motor1_PWM,Param.Motor2_PWM);
    MyPrintf("%d,%d,%.2f\r\n",Param.Motor1_PWM,Param.Motor2_PWM,Param.Distance_Motor1Curret);

    /* 任务执行时间监控 */
    static uint8_t monitor_counter = 0;
    monitor_counter++;
    if(monitor_counter >= 25) // 每5秒输出一次任务时间统计 (200ms * 25 = 5000ms)
    {
        monitor_counter = 0;
        uint16_t pid_time = 0, encoder_time = 0;
        if(Task_GetMaxUsed("PID", &pid_time))
        {
            MyPrintf("PID_MaxUsed:%dms\r\n", pid_time);
        }
        if(Task_GetMaxUsed("Encoder", &encoder_time))
        {
            MyPrintf("Encoder_MaxUsed:%dms\r\n", encoder_time);
        }
    }
}

void Task_Encoder(void *para)
{
    //获取单位时间内的脉冲数
	Param.UnitTime_Motor1Pluse=read_encoder(MOTOR_FONT_LEFT);
	Param.UnitTime_Motor2Pluse=read_encoder(MOTOR_FONT_RIGHT);
    /*更新小车当前的行驶距离:(周期内累计脉冲/(车轮走一圈产生的脉冲数)*(车轮周长))*/
	Param.Distance_Motor1Curret = ((float)Data_MotorEncoder[0]/(TOTAL_MOTOR_RESOLUTION))*(2*3.14*WHEEL_R);
}

// PID算法运算
void Task_PID(void *para)
{
    long 	average_pluse;

    /* 判断是否开启巡线功能 */
    if(Flag.Start_Line == 1)
    {
        //判断是否到了目标距离的最小阈值,到达即开始停车
        if((Param.Distance_Motor1Curret<=Param.Distance_TargetThreshold + CAR_LENGTH )&&(Param.Distance_Motor1Curret>=Param.Distance_TargetThreshold - CAR_LENGTH ))
        {
            if(++stop_time_cnt == time)//time s
            {
                stop_time_cnt=0;
                Flag.Start_Line = 0;
                Flag.Stop_Car = 1;
                Load_Motor_PWM(0,0);
                return;
            }
        }
        else
        {
            stop_time_cnt=0;
            Flag.Stop_Car = 0;
        }
        //判断电机是否启动
        if(Flag.Is_EnMOTOR == 1)
        {
            LocationRing_VelocityRing_Control();
            //解决车未走直线或者打滑的情况导致的两轮累计脉冲数不相等的情况
            if(Param.Line_TempOut==0)
            {
                average_pluse = (Data_MotorEncoder[0]+Data_MotorEncoder[1])/2;
                Data_MotorEncoder[0] = Data_MotorEncoder[1] = average_pluse;
            }
        }

        //巡线补偿
                Param.Motor1_PWM = PID.Velocity_Out+Param.Line_TempOut;
                Param.Motor2_PWM = PID.Velocity_Out-Param.Line_TempOut;

                // 【测试模式】暂时禁用PWM变化率保护，观察PID原始响应特性
                // static int16_t last_motor1_pwm = 0, last_motor2_pwm = 0;
                // #define MAX_PWM_CHANGE 30 //最大PWM变化量限制

                // // 限制PWM变化率，防止突变
                // if (Param.Motor1_PWM - last_motor1_pwm > MAX_PWM_CHANGE)
                //     Param.Motor1_PWM = last_motor1_pwm + MAX_PWM_CHANGE;
                // else if (last_motor1_pwm - Param.Motor1_PWM > MAX_PWM_CHANGE)
                //     Param.Motor1_PWM = last_motor1_pwm - MAX_PWM_CHANGE;

                // if (Param.Motor2_PWM - last_motor2_pwm > MAX_PWM_CHANGE)
                //     Param.Motor2_PWM = last_motor2_pwm + MAX_PWM_CHANGE;
                // else if (last_motor2_pwm - Param.Motor2_PWM > MAX_PWM_CHANGE)
                //     Param.Motor2_PWM = last_motor2_pwm - MAX_PWM_CHANGE;

                // PWM限制为0-30，禁止负值
                if (Param.Motor1_PWM > 30) Param.Motor1_PWM = 30;
                else if (Param.Motor1_PWM < 0) Param.Motor1_PWM = 0;
                if (Param.Motor2_PWM > 30) Param.Motor2_PWM = 30;
                else if (Param.Motor2_PWM < 0) Param.Motor2_PWM = 0;

                // last_motor1_pwm = Param.Motor1_PWM;
                // last_motor2_pwm = Param.Motor2_PWM;

                Load_Motor_PWM(Param.Motor1_PWM,Param.Motor2_PWM);
    }
}