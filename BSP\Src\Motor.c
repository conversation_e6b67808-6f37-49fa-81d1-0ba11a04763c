#include "Motor.h"

#define SET_FOWARD(X)   DL_GPIO_setPins(DIRC_CTRL_PORT, (X))
#define SET_BACKWARD(X) DL_GPIO_clearPins(DIRC_CTRL_PORT, (X))

/**
 * @brief 开启电机
 *
 */
void Motor_Start(void)
{
    DL_TimerG_startCounter(MotorFront_INST); //开启前轮
    // DL_TimerG_startCounter(MotorBack_INST); //开启后轮

    //电机都停止运行
    Motor_SetPWM(MOTOR_FONT_LEFT, 0);
    Motor_SetPWM(MOTOR_FONT_RIGHT, 0);
    // Motor_SetPWM(MOTOR_BACK_LEFT, 0);
    // Motor_SetPWM(MOTOR_BACK_RIGHT, 0);

    //四轮都正转
    Motor_SetDirc(MOTOR_FONT_LEFT, DIRC_FOWARD);
    Motor_SetDirc(MOTOR_FONT_RIGHT, DIRC_FOWARD);
    // Motor_SetDirc(MOTOR_BACK_LEFT, DIRC_FOWARD);
    // Motor_SetDirc(MOTOR_BACK_RIGHT, DIRC_FOWARD);
}

/**
 * @brief 设置对应的电机PWM值
 *
 * @param Motor_Num  电机编号
 * @param PWM_Duty PWM占空比
 * @return 返回设置成功与否
 */
bool Motor_SetPWM(MOTOR_Def_t Motor_Num, uint8_t PWM_Duty)
{
    if (PWM_Duty > 100) return false;

    switch (Motor_Num)
    {
        case MOTOR_FONT_LEFT: //设置左前轮
            DL_TimerG_setCaptureCompareValue(MotorFront_INST, PWM_Duty, DL_TIMER_CC_0_INDEX);
            return true;

        case MOTOR_FONT_RIGHT: //设置右前轮
            DL_TimerG_setCaptureCompareValue(MotorFront_INST, PWM_Duty, DL_TIMER_CC_1_INDEX);
            return true;

        // case MOTOR_BACK_LEFT: //设置左后轮
        //     DL_TimerG_setCaptureCompareValue(MotorBack_INST, PWM_Duty, DL_TIMER_CC_0_INDEX);
        //     return true;

        // case MOTOR_BACK_RIGHT: //设置右后轮
        //     DL_TimerG_setCaptureCompareValue(MotorBack_INST, PWM_Duty, DL_TIMER_CC_1_INDEX);
        //     return true;

        default: //未知轮
            return false;
    }
}

/**
 * @brief 设置电机正反转
 *
 * @param Motor_Num 电机编号
 * @param DIRC 方向
 * @return 返回设置成功与否
 */
bool Motor_SetDirc(MOTOR_Def_t Motor_Num, MOTOR_Def_t DIRC)
{
    if (DIRC != DIRC_FOWARD && DIRC != DIRC_BACKWARD) return false;

    if (DIRC == DIRC_FOWARD) SET_FOWARD(Motor_Num);
    else SET_BACKWARD(Motor_Num);
    return true;
}

/**
 * @brief 读取单位时间脉冲数
 *
 * @param Motor_Num 电机编号
 * @return 返回单位时间脉冲数
 */
int16_t read_encoder(MOTOR_Def_t Motor_Num)
{
    static int16_t Data_MotorEncoder_Old[2] = {0}; //编码器旧值
    int16_t Data_MotorEncoder_Temp; //编码器暂存值

    #define MAX_ENCODER_CHANGE 50 //最大编码器变化量限制

    switch (Motor_Num)
    {
        case MOTOR_FONT_LEFT: //读取左前轮
            Data_MotorEncoder_Temp = Data_MotorEncoder[0] - Data_MotorEncoder_Old[0]; //计算单位周期编码器差值

            // 异常检测：限制编码器变化率
            if (Data_MotorEncoder_Temp > MAX_ENCODER_CHANGE) Data_MotorEncoder_Temp = MAX_ENCODER_CHANGE;
            else if (Data_MotorEncoder_Temp < -MAX_ENCODER_CHANGE) Data_MotorEncoder_Temp = -MAX_ENCODER_CHANGE;

            Data_MotorEncoder_Old[0] = Data_MotorEncoder[0];
            return Data_MotorEncoder_Temp;

        case MOTOR_FONT_RIGHT: //读取+右前轮
            Data_MotorEncoder_Temp = Data_MotorEncoder[1] - Data_MotorEncoder_Old[1]; //计算单位周期编码器差值

            // 异常检测：限制编码器变化率
            if (Data_MotorEncoder_Temp > MAX_ENCODER_CHANGE) Data_MotorEncoder_Temp = MAX_ENCODER_CHANGE;
            else if (Data_MotorEncoder_Temp < -MAX_ENCODER_CHANGE) Data_MotorEncoder_Temp = -MAX_ENCODER_CHANGE;

            Data_MotorEncoder_Old[1] = Data_MotorEncoder[1];
            return Data_MotorEncoder_Temp;

        default: //未知轮
            return 0;
    }
}

/**
 * 函数名：Load_Motor_PWM
 * 描述：更新PWM并将其写入定时器
 * 输入：MOTOR1_PWM-左轮电机计算得出的PWM值,MOTOR2_PWM-右轮电机计算得出的PWM值
 * 输出：无
 */
void Load_Motor_PWM(int MOTOR1_PWM,int MOTOR2_PWM)
{
	//确保PWM值为正值，范围0-80
	MOTOR1_PWM = (MOTOR1_PWM > 0) ? MOTOR1_PWM : 0;  //负值设为0
	MOTOR2_PWM = (MOTOR2_PWM > 0) ? MOTOR2_PWM : 0;  //负值设为0

	//限制PWM最大值为80
	MOTOR1_PWM = (MOTOR1_PWM > PWM_MAX) ? PWM_MAX : MOTOR1_PWM;
	MOTOR2_PWM = (MOTOR2_PWM > PWM_MAX) ? PWM_MAX : MOTOR2_PWM;

	//PWM Set
    Motor_SetPWM(MOTOR_FONT_LEFT,  MOTOR1_PWM);
    Motor_SetPWM(MOTOR_FONT_RIGHT, MOTOR2_PWM);

}